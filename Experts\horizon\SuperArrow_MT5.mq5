//+------------------------------------------------------------------+
//|                                              SuperArrow_MT5.mq5 |
//|                        Copyright 2025, Emre USUN - Horizon Team |
//|                      Super Arrow Indicator - MQ4'den MQ5'e çevrildi |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Emre USUN - Horizon Team"
#property link      "https://github.com/emreusun87"
#property version   "1.0"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- Plot ayarları
#property indicator_label1  "BUY"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

#property indicator_label2  "SELL"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

//--- Input parameters
input group "=== Moving Average Ayarları ==="
input int FasterMovingAverage = 5;         // Hızlı MA Periyodu
input int SlowerMovingAverage = 12;        // Yavaş MA Periyodu

input group "=== RSI Ayarları ==="
input int RSIPeriod = 12;                  // RSI Periyodu

input group "=== Bollinger Bands Ayarları ==="
input int BollingerbandsPeriod = 10;       // BB Periyodu
input int BollingerbandsShift = 0;         // BB Shift
input double BollingerbandsDeviation = 0.5; // BB Sapma

input group "=== Bulls/Bears Power Ayarları ==="
input int BullsPowerPeriod = 50;           // Bulls Power Periyodu
input int BearsPowerPeriod = 50;           // Bears Power Periyodu

input group "=== Diğer Ayarlar ==="
input int MagicFilterPeriod = 1;           // Magic Filter Periyodu
input int Utstup = 10;                     // Arrow mesafesi (point)
input bool Alerts = true;                  // Alert'leri etkinleştir

input group "=== Bildirim Ayarları ==="
input bool popup_alert = false;            // Popup mesajı
input bool notification_alert = false;     // Push bildirimi
input bool email_alert = false;            // Email bildirimi
input bool play_sound = false;             // Ses çal
input string sound_file = "";              // Ses dosyası

//--- Indicator buffers
double BuyArrowBuffer[];
double SellArrowBuffer[];

//--- Global variables
int fastMA_handle;                         // Hızlı MA handle
int slowMA_handle;                         // Yavaş MA handle
int rsi_handle;                            // RSI handle
int bb_handle;                             // Bollinger Bands handle
int bulls_handle;                          // Bulls Power handle
int bears_handle;                          // Bears Power handle

// Sinyal durumları
bool last_buy_signal = false;
bool last_sell_signal = false;
datetime last_alert_time = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Buffer ayarları
    SetIndexBuffer(0, BuyArrowBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SellArrowBuffer, INDICATOR_DATA);
    
    // Arrow kodları
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // BUY arrow (yukarı)
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // SELL arrow (aşağı)
    
    // Buffer'ları başlat
    ArraySetAsSeries(BuyArrowBuffer, true);
    ArraySetAsSeries(SellArrowBuffer, true);

    // Buffer'ları EMPTY_VALUE ile doldur
    ArrayInitialize(BuyArrowBuffer, EMPTY_VALUE);
    ArrayInitialize(SellArrowBuffer, EMPTY_VALUE);
    
    // Handle'ları oluştur
    fastMA_handle = iMA(_Symbol, _Period, FasterMovingAverage, 0, MODE_EMA, PRICE_CLOSE);
    slowMA_handle = iMA(_Symbol, _Period, SlowerMovingAverage, 0, MODE_EMA, PRICE_CLOSE);
    rsi_handle = iRSI(_Symbol, _Period, RSIPeriod, PRICE_CLOSE);
    bb_handle = iBands(_Symbol, _Period, BollingerbandsPeriod, BollingerbandsShift, BollingerbandsDeviation, PRICE_CLOSE);
    bulls_handle = iBullsPower(_Symbol, _Period, BullsPowerPeriod);
    bears_handle = iBearsPower(_Symbol, _Period, BearsPowerPeriod);
    
    // Handle kontrolü
    if(fastMA_handle == INVALID_HANDLE || slowMA_handle == INVALID_HANDLE ||
       rsi_handle == INVALID_HANDLE || bb_handle == INVALID_HANDLE ||
       bulls_handle == INVALID_HANDLE || bears_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle'ları oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Indicator ismi
    IndicatorSetString(INDICATOR_SHORTNAME, "SuperArrow(MA:" + IntegerToString(FasterMovingAverage) + 
                      "/" + IntegerToString(SlowerMovingAverage) + ",RSI:" + IntegerToString(RSIPeriod) + ")");
    
    Print("📊 Super Arrow MT5 başlatıldı");
    Print("   Hızlı MA: ", FasterMovingAverage, " | Yavaş MA: ", SlowerMovingAverage);
    Print("   RSI: ", RSIPeriod, " | BB: ", BollingerbandsPeriod);
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // Yeterli veri var mı kontrol et
    if(rates_total < SlowerMovingAverage + BullsPowerPeriod + 20) return 0;
    
    // Handle'lar hazır mı kontrol et
    if(BarsCalculated(fastMA_handle) < rates_total || BarsCalculated(slowMA_handle) < rates_total ||
       BarsCalculated(rsi_handle) < rates_total || BarsCalculated(bb_handle) < rates_total ||
       BarsCalculated(bulls_handle) < rates_total || BarsCalculated(bears_handle) < rates_total)
        return 0;
    
    // Buffer'ları hazırla
    double fastMA[], slowMA[], rsi_values[];
    double bb_upper[], bb_lower[];
    double bulls_power[], bears_power[];
    
    ArraySetAsSeries(fastMA, true);
    ArraySetAsSeries(slowMA, true);
    ArraySetAsSeries(rsi_values, true);
    ArraySetAsSeries(bb_upper, true);
    ArraySetAsSeries(bb_lower, true);
    ArraySetAsSeries(bulls_power, true);
    ArraySetAsSeries(bears_power, true);
    
    // Veri kopyala
    int copy_bars = rates_total - prev_calculated + 20;
    if(prev_calculated == 0) copy_bars = rates_total;
    
    if(CopyBuffer(fastMA_handle, 0, 0, copy_bars, fastMA) <= 0) return 0;
    if(CopyBuffer(slowMA_handle, 0, 0, copy_bars, slowMA) <= 0) return 0;
    if(CopyBuffer(rsi_handle, 0, 0, copy_bars, rsi_values) <= 0) return 0;
    if(CopyBuffer(bb_handle, 1, 0, copy_bars, bb_upper) <= 0) return 0;    // Upper band
    if(CopyBuffer(bb_handle, 2, 0, copy_bars, bb_lower) <= 0) return 0;    // Lower band
    if(CopyBuffer(bulls_handle, 0, 0, copy_bars, bulls_power) <= 0) return 0;
    if(CopyBuffer(bears_handle, 0, 0, copy_bars, bears_power) <= 0) return 0;
    
    // Array'leri time series olarak ayarla
    ArraySetAsSeries(time, true);
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    // Hesaplama başlangıç noktası
    int start = prev_calculated;
    if(start == 0) start = SlowerMovingAverage + BullsPowerPeriod + 20;
    
    // Ana hesaplama döngüsü
    for(int i = start; i < rates_total - 1; i++)
    {
        // Buffer index'leri (time series)
        int current_index = rates_total - 1 - i;
        int prev_index = current_index + 1;
        
        // Buffer'ları temizle
        BuyArrowBuffer[current_index] = EMPTY_VALUE;
        SellArrowBuffer[current_index] = EMPTY_VALUE;
        
        // Magic Filter hesapla
        double magic_filter = CalculateMagicFilter(current_index);
        bool magic_bullish = magic_filter >= 0.0;
        bool magic_bearish = magic_filter < 0.0;

        // Debug için koşulları ayrı ayrı kontrol et
        bool ma_cross_buy = (fastMA[current_index] > slowMA[current_index]) && (fastMA[prev_index] <= slowMA[prev_index]);
        bool ma_cross_sell = (fastMA[current_index] < slowMA[current_index]) && (fastMA[prev_index] >= slowMA[prev_index]);
        bool rsi_cross_buy = (rsi_values[current_index] > 50.0) && (rsi_values[prev_index] <= 50.0);
        bool rsi_cross_sell = (rsi_values[current_index] < 50.0) && (rsi_values[prev_index] >= 50.0);

        // Basitleştirilmiş sinyal koşulları (test için)
        bool simple_buy = ma_cross_buy || rsi_cross_buy;
        bool simple_sell = ma_cross_sell || rsi_cross_sell;

        // Sinyal koşullarını kontrol et
        bool buy_conditions = CheckBuyConditions(current_index, prev_index, fastMA, slowMA, rsi_values,
                                                bb_lower, bears_power, magic_bullish);
        bool sell_conditions = CheckSellConditions(current_index, prev_index, fastMA, slowMA, rsi_values,
                                                  bb_upper, bulls_power, magic_bearish);

        // Debug log (sadece son 10 bar için)
        if(i >= rates_total - 10)
        {
            Print("Bar ", i, " (", TimeToString(time[current_index]), "):");
            Print("  MA Cross BUY: ", ma_cross_buy, " | SELL: ", ma_cross_sell);
            Print("  RSI Cross BUY: ", rsi_cross_buy, " | SELL: ", rsi_cross_sell);
            Print("  Magic Filter: ", magic_filter, " (Bull:", magic_bullish, ", Bear:", magic_bearish, ")");
            Print("  Final BUY: ", buy_conditions, " | SELL: ", sell_conditions);
        }

        // BUY Sinyali (basitleştirilmiş test için)
        if(simple_buy)
        {
            BuyArrowBuffer[current_index] = low[current_index] - (Utstup * _Point);
            Print("🟢 BUY Arrow eklendi - Bar: ", i, " Fiyat: ", DoubleToString(BuyArrowBuffer[current_index], _Digits));

            // Alert (sadece en son bar için ve tekrar etmesin)
            if(i == rates_total - 2 && time[current_index] != last_alert_time && !last_buy_signal)
            {
                SendAlert("BUY", close[current_index]);
                last_alert_time = time[current_index];
                last_buy_signal = true;
                last_sell_signal = false;
            }
        }

        // SELL Sinyali (basitleştirilmiş test için)
        if(simple_sell)
        {
            SellArrowBuffer[current_index] = high[current_index] + (Utstup * _Point);
            Print("🔴 SELL Arrow eklendi - Bar: ", i, " Fiyat: ", DoubleToString(SellArrowBuffer[current_index], _Digits));

            // Alert (sadece en son bar için ve tekrar etmesin)
            if(i == rates_total - 2 && time[current_index] != last_alert_time && !last_sell_signal)
            {
                SendAlert("SELL", close[current_index]);
                last_alert_time = time[current_index];
                last_sell_signal = true;
                last_buy_signal = false;
            }
        }
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| Magic Filter hesapla                                            |
//+------------------------------------------------------------------+
double CalculateMagicFilter(int index)
{
    // Basit magic filter implementasyonu
    double highest = 0, lowest = 999999;

    for(int j = 0; j < MagicFilterPeriod; j++)
    {
        double high_val = iHigh(_Symbol, _Period, index + j);
        double low_val = iLow(_Symbol, _Period, index + j);

        if(high_val > highest) highest = high_val;
        if(low_val < lowest) lowest = low_val;
    }

    double range = highest - lowest;
    if(range == 0) return 0;

    double current_close = iClose(_Symbol, _Period, index);
    return (current_close - lowest) / range - 0.5; // -0.5 ile 0.5 arası normalize
}

//+------------------------------------------------------------------+
//| BUY koşullarını kontrol et                                      |
//+------------------------------------------------------------------+
bool CheckBuyConditions(int current, int prev, double &fastMA[], double &slowMA[],
                       double &rsi[], double &bb_lower[], double &bears[], bool magic_bullish)
{
    // 1. MA Cross: Hızlı MA > Yavaş MA (ve önceden tersi)
    bool ma_cross = (fastMA[current] > slowMA[current]) && (fastMA[prev] <= slowMA[prev]);

    // 2. RSI: RSI > 50 (ve önceden < 50)
    bool rsi_cross = (rsi[current] > 50.0) && (rsi[prev] <= 50.0);

    // 3. Bollinger: Close < Alt Band
    double current_close = iClose(_Symbol, _Period, current);
    double prev_close = iClose(_Symbol, _Period, prev);
    bool bb_condition = (current_close < bb_lower[current]) && (prev_close <= bb_lower[prev]);

    // 4. Bears Power: < 0 ve azalıyor (güçleniyor)
    bool bears_condition = (bears[current] < 0.0) && (bears[prev] < bears[current]);

    // 5. Magic Filter: Pozitif
    bool magic_condition = magic_bullish;

    return ma_cross && rsi_cross && bb_condition && bears_condition && magic_condition;
}

//+------------------------------------------------------------------+
//| SELL koşullarını kontrol et                                     |
//+------------------------------------------------------------------+
bool CheckSellConditions(int current, int prev, double &fastMA[], double &slowMA[],
                        double &rsi[], double &bb_upper[], double &bulls[], bool magic_bearish)
{
    // 1. MA Cross: Hızlı MA < Yavaş MA (ve önceden tersi)
    bool ma_cross = (fastMA[current] < slowMA[current]) && (fastMA[prev] >= slowMA[prev]);

    // 2. RSI: RSI < 50 (ve önceden > 50)
    bool rsi_cross = (rsi[current] < 50.0) && (rsi[prev] >= 50.0);

    // 3. Bollinger: Close > Üst Band
    double current_close = iClose(_Symbol, _Period, current);
    double prev_close = iClose(_Symbol, _Period, prev);
    bool bb_condition = (current_close > bb_upper[current]) && (prev_close >= bb_upper[prev]);

    // 4. Bulls Power: > 0 ama azalıyor (zayıflıyor)
    bool bulls_condition = (bulls[current] > 0.0) && (bulls[prev] > bulls[current]);

    // 5. Magic Filter: Negatif
    bool magic_condition = magic_bearish;

    return ma_cross && rsi_cross && bb_condition && bulls_condition && magic_condition;
}

//+------------------------------------------------------------------+
//| Alert gönder                                                     |
//+------------------------------------------------------------------+
void SendAlert(string signal_type, double price)
{
    if(!Alerts) return;

    string message = _Symbol + " " + GetTimeFrameString(_Period) + " " + signal_type +
                    " @ " + DoubleToString(price, _Digits);

    if(popup_alert)
        Alert(message);

    if(notification_alert)
        SendNotification(message);

    if(email_alert)
        SendMail("Super Arrow Signal", message);

    if(play_sound && sound_file != "")
        PlaySound(sound_file);

    Print("🔔 ", signal_type, " sinyali: ", message);
}

//+------------------------------------------------------------------+
//| Timeframe string'e çevir                                         |
//+------------------------------------------------------------------+
string GetTimeFrameString(ENUM_TIMEFRAMES period)
{
    switch(period)
    {
        case PERIOD_M1:  return "M1";
        case PERIOD_M5:  return "M5";
        case PERIOD_M15: return "M15";
        case PERIOD_M30: return "M30";
        case PERIOD_H1:  return "H1";
        case PERIOD_H4:  return "H4";
        case PERIOD_D1:  return "D1";
        case PERIOD_W1:  return "W1";
        case PERIOD_MN1: return "MN1";
        default:         return IntegerToString(period);
    }
}

//+------------------------------------------------------------------+
//| Indicator temizleme                                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Handle'ları serbest bırak
    if(fastMA_handle != INVALID_HANDLE) IndicatorRelease(fastMA_handle);
    if(slowMA_handle != INVALID_HANDLE) IndicatorRelease(slowMA_handle);
    if(rsi_handle != INVALID_HANDLE) IndicatorRelease(rsi_handle);
    if(bb_handle != INVALID_HANDLE) IndicatorRelease(bb_handle);
    if(bulls_handle != INVALID_HANDLE) IndicatorRelease(bulls_handle);
    if(bears_handle != INVALID_HANDLE) IndicatorRelease(bears_handle);

    Print("📊 Super Arrow MT5 durduruldu");
}
