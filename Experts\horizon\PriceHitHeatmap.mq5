//+------------------------------------------------------------------+
//|                                            PriceHitHeatmap.mq5  |
//|                        Copyright 2025, Emre USUN - Horizon Team |
//|                                   Price Hit Frequency Heatmap   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Emre USUN - Horizon Team"
#property link      "https://github.com/emreusun87"
#property version   "1.0"
#property indicator_chart_window
#property indicator_buffers 0
#property indicator_plots   0

//--- Input parameters
input int TimeWindowMinutes = 60;          // Ana<PERSON>z zaman penceresi (dakika)
input double PriceLevelPips = 1.0;         // Fiyat seviyesi gruplaması (pip)
input int MinHitCount = 3;                 // Minimum hit sayısı (heatmap için)
input int HeatmapBars = 10;                // Heatmap genişliği (bar sayısı)
input bool ShowHotestPrice = true;         // En sıcak fiyatı işaretle
input bool ShowDebugInfo = false;          // Debug bilgisi göster
input int UpdateFrequency = 5;             // Güncelleme sıklığı (saniye)

//--- Heatmap renk ayarları
input color ColdColor = clrBlue;           // Soğuk renk (az hit)
input color WarmColor = clrYellow;         // Ilık renk (orta hit)
input color HotColor = clrRed;             // Sıcak renk (çok hit)
input int MaxTransparency = 80;            // Maksimum şeffaflık (0-100)

//--- Global variables
struct PriceHit
{
    double price;
    datetime time;
};

struct HeatmapLevel
{
    double price;
    int hit_count;
    color level_color;
    int transparency;
};

PriceHit price_buffer[];                   // Circular buffer
int buffer_size = 10000;                   // Buffer boyutu
int buffer_head = 0;                       // Buffer başlangıcı
int buffer_count = 0;                      // Aktif veri sayısı
double price_level_size;                   // Fiyat seviyesi boyutu
datetime last_update_time = 0;             // Son güncelleme zamanı
int heatmap_counter = 0;                   // Heatmap object sayacı

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Price level boyutunu hesapla
    price_level_size = PriceLevelPips * _Point;
    if(_Digits == 5 || _Digits == 3) price_level_size *= 10;
    
    // Buffer'ı başlat
    ArrayResize(price_buffer, buffer_size);
    
    // Indicator ismi
    IndicatorSetString(INDICATOR_SHORTNAME, "PriceHitHeatmap(" + IntegerToString(TimeWindowMinutes) + "m)");
    
    Print("🔥 Price Hit Heatmap Indicator başlatıldı");
    Print("   Zaman Penceresi: ", TimeWindowMinutes, " dakika");
    Print("   Fiyat Seviyesi: ", DoubleToString(PriceLevelPips, 1), " pip");
    Print("   Heatmap Genişliği: ", HeatmapBars, " bar");
    Print("   Güncelleme: Her ", UpdateFrequency, " saniye");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // Her tick'te fiyatı buffer'a ekle
    AddPriceToBuffer(close[rates_total-1], time[rates_total-1]);
    
    // Belirli aralıklarla heatmap güncelle
    if(TimeCurrent() - last_update_time >= UpdateFrequency)
    {
        UpdateHeatmap(time[rates_total-1]);
        last_update_time = TimeCurrent();
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| Buffer'a fiyat ekle                                              |
//+------------------------------------------------------------------+
void AddPriceToBuffer(double price, datetime time)
{
    // Fiyatı seviyeye yuvarla
    double rounded_price = RoundToLevel(price, price_level_size);
    
    // Buffer'a ekle
    price_buffer[buffer_head].price = rounded_price;
    price_buffer[buffer_head].time = time;
    
    // Buffer head'i ilerlet
    buffer_head = (buffer_head + 1) % buffer_size;
    
    // Count'u artır (max buffer_size'a kadar)
    if(buffer_count < buffer_size) buffer_count++;
}

//+------------------------------------------------------------------+
//| Fiyatı seviyeye yuvarla                                          |
//+------------------------------------------------------------------+
double RoundToLevel(double price, double level)
{
    return MathRound(price / level) * level;
}

//+------------------------------------------------------------------+
//| Heatmap güncelle                                                 |
//+------------------------------------------------------------------+
void UpdateHeatmap(datetime current_time)
{
    // Eski heatmap'i temizle
    ClearOldHeatmap();
    
    // Hit analizi yap
    HeatmapLevel levels[];
    int level_count = AnalyzePriceHits(levels);
    
    if(level_count == 0) return;
    
    // Heatmap çiz
    DrawHeatmap(levels, level_count, current_time);
    
    // En sıcak fiyatı işaretle
    if(ShowHotestPrice)
    {
        double hottest_price = FindHottestPrice(levels, level_count);
        if(hottest_price > 0)
            MarkHottestPrice(current_time, hottest_price);
    }
    
    // Debug bilgisi
    if(ShowDebugInfo)
    {
        Print("🔥 Heatmap güncellendi - ", level_count, " seviye, Zaman: ", TimeToString(current_time));
    }
}

//+------------------------------------------------------------------+
//| Price hit analizi                                                |
//+------------------------------------------------------------------+
int AnalyzePriceHits(HeatmapLevel &levels[])
{
    if(buffer_count == 0) return 0;
    
    // Zaman sınırını hesapla
    datetime time_limit = TimeCurrent() - TimeWindowMinutes * 60;
    
    // Geçici hit sayıları
    double temp_prices[];
    int temp_hits[];
    int unique_count = 0;
    
    // Buffer'ı tara
    for(int i = 0; i < buffer_count; i++)
    {
        int index = (buffer_head - 1 - i + buffer_size) % buffer_size;
        
        // Zaman kontrolü
        if(price_buffer[index].time < time_limit) continue;
        
        double price = price_buffer[index].price;
        
        // Bu fiyat daha önce sayıldı mı?
        bool found = false;
        for(int j = 0; j < unique_count; j++)
        {
            if(MathAbs(temp_prices[j] - price) < _Point)
            {
                temp_hits[j]++;
                found = true;
                break;
            }
        }
        
        // Yeni fiyat
        if(!found)
        {
            ArrayResize(temp_prices, unique_count + 1);
            ArrayResize(temp_hits, unique_count + 1);
            temp_prices[unique_count] = price;
            temp_hits[unique_count] = 1;
            unique_count++;
        }
    }
    
    // Minimum hit sayısını geçenleri filtrele
    int valid_count = 0;
    for(int i = 0; i < unique_count; i++)
    {
        if(temp_hits[i] >= MinHitCount)
            valid_count++;
    }
    
    if(valid_count == 0) return 0;
    
    // Levels array'ini hazırla
    ArrayResize(levels, valid_count);
    int level_index = 0;
    
    // Max hit sayısını bul (renk skalası için)
    int max_hits = 0;
    for(int i = 0; i < unique_count; i++)
    {
        if(temp_hits[i] >= MinHitCount && temp_hits[i] > max_hits)
            max_hits = temp_hits[i];
    }
    
    // Levels'ı doldur
    for(int i = 0; i < unique_count; i++)
    {
        if(temp_hits[i] >= MinHitCount)
        {
            levels[level_index].price = temp_prices[i];
            levels[level_index].hit_count = temp_hits[i];
            
            // Renk ve şeffaflık hesapla
            CalculateHeatmapColor(levels[level_index], max_hits);
            
            level_index++;
        }
    }
    
    return valid_count;
}

//+------------------------------------------------------------------+
//| Heatmap renk hesaplama                                           |
//+------------------------------------------------------------------+
void CalculateHeatmapColor(HeatmapLevel &level, int max_hits)
{
    // Hit yoğunluğu oranı (0.0 - 1.0)
    double intensity = (double)level.hit_count / max_hits;
    
    // Renk gradyanı hesapla
    if(intensity <= 0.33)
    {
        // Soğuk (mavi)
        level.level_color = ColdColor;
        level.transparency = MaxTransparency - (int)(intensity * 30);
    }
    else if(intensity <= 0.66)
    {
        // Ilık (sarı)
        level.level_color = WarmColor;
        level.transparency = MaxTransparency - (int)(intensity * 50);
    }
    else
    {
        // Sıcak (kırmızı)
        level.level_color = HotColor;
        level.transparency = MaxTransparency - (int)(intensity * 70);
    }
    
    // Şeffaflık sınırları
    if(level.transparency < 10) level.transparency = 10;
    if(level.transparency > MaxTransparency) level.transparency = MaxTransparency;
}

//+------------------------------------------------------------------+
//| Heatmap çiz                                                      |
//+------------------------------------------------------------------+
void DrawHeatmap(HeatmapLevel &levels[], int level_count, datetime current_time)
{
    for(int i = 0; i < level_count; i++)
    {
        // Heatmap dikdörtgeni çiz
        string rect_name = "Heatmap_" + IntegerToString(heatmap_counter++);
        
        datetime start_time = current_time;
        datetime end_time = current_time + HeatmapBars * PeriodSeconds(_Period);
        double top_price = levels[i].price + (price_level_size / 2);
        double bottom_price = levels[i].price - (price_level_size / 2);
        
        // Dikdörtgen oluştur
        ObjectCreate(0, rect_name, OBJ_RECTANGLE, 0, start_time, top_price, end_time, bottom_price);
        ObjectSetInteger(0, rect_name, OBJPROP_COLOR, levels[i].level_color);
        ObjectSetInteger(0, rect_name, OBJPROP_FILL, true);
        ObjectSetInteger(0, rect_name, OBJPROP_BACK, true);
        ObjectSetInteger(0, rect_name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(0, rect_name, OBJPROP_HIDDEN, true);
        
        // Şeffaflık ayarla (0-255 arası)
        int alpha = (255 * levels[i].transparency) / 100;
        ObjectSetInteger(0, rect_name, OBJPROP_BGCOLOR, levels[i].level_color + (alpha << 24));
        
        // Tooltip ekle
        string tooltip = "Price: " + DoubleToString(levels[i].price, _Digits) +
                        " | Hits: " + IntegerToString(levels[i].hit_count);
        ObjectSetString(0, rect_name, OBJPROP_TOOLTIP, tooltip);
    }
}

//+------------------------------------------------------------------+
//| En sıcak fiyatı bul                                              |
//+------------------------------------------------------------------+
double FindHottestPrice(HeatmapLevel &levels[], int level_count)
{
    if(level_count == 0) return 0;

    int max_hits = 0;
    double hottest_price = 0;

    for(int i = 0; i < level_count; i++)
    {
        if(levels[i].hit_count > max_hits)
        {
            max_hits = levels[i].hit_count;
            hottest_price = levels[i].price;
        }
    }

    return hottest_price;
}

//+------------------------------------------------------------------+
//| En sıcak fiyatı işaretle                                         |
//+------------------------------------------------------------------+
void MarkHottestPrice(datetime time, double price)
{
    string marker_name = "HottestPrice_" + IntegerToString(heatmap_counter);

    // Arrow object oluştur
    ObjectCreate(0, marker_name, OBJ_ARROW, 0, time, price);
    ObjectSetInteger(0, marker_name, OBJPROP_ARROWCODE, 159);  // Nokta
    ObjectSetInteger(0, marker_name, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, marker_name, OBJPROP_WIDTH, 5);
    ObjectSetInteger(0, marker_name, OBJPROP_BACK, false);

    // Text label ekle
    string label_name = "HottestLabel_" + IntegerToString(heatmap_counter);
    ObjectCreate(0, label_name, OBJ_TEXT, 0, time, price);
    ObjectSetString(0, label_name, OBJPROP_TEXT, "🔥 HOT");
    ObjectSetInteger(0, label_name, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, label_name, OBJPROP_FONTSIZE, 10);
    ObjectSetInteger(0, label_name, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
}

//+------------------------------------------------------------------+
//| Eski heatmap temizle                                             |
//+------------------------------------------------------------------+
void ClearOldHeatmap()
{
    int total = ObjectsTotal(0);
    for(int i = total - 1; i >= 0; i--)
    {
        string name = ObjectName(0, i);
        if(StringFind(name, "Heatmap_") >= 0 ||
           StringFind(name, "HottestPrice_") >= 0 ||
           StringFind(name, "HottestLabel_") >= 0)
        {
            ObjectDelete(0, name);
        }
    }
}

//+------------------------------------------------------------------+
//| Indicator temizleme                                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Tüm heatmap object'leri temizle
    ClearOldHeatmap();

    Print("🔥 Price Hit Heatmap Indicator durduruldu");
}
