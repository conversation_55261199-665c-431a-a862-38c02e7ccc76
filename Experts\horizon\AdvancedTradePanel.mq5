//+------------------------------------------------------------------+
//| AdvancedTradePanel.mq5 - G<PERSON><PERSON><PERSON>ş Sürüklenebilir İşlem Paneli  |
//| Her zaman en üstte kalan, sürüklenebilir işlem yönetim paneli   |
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Gelişmiş Sürüklenebilir İşlem Yönetim Paneli"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| <PERSON>                                                  |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     380
#define PANEL_HEIGHT    450
#define HEADER_HEIGHT   30
#define BUTTON_HEIGHT   25
#define ROW_HEIGHT      18
#define MARGIN          8

//+------------------------------------------------------------------+
//| Renkler                                                         |
//+------------------------------------------------------------------+
#define COLOR_BACKGROUND    C'30,30,30'
#define COLOR_HEADER        C'0,120,215'
#define COLOR_BORDER        C'70,70,70'
#define COLOR_TEXT          clrWhite
#define COLOR_PROFIT        clrLimeGreen
#define COLOR_LOSS          clrTomato
#define COLOR_BUTTON_BUY    C'0,128,0'
#define COLOR_BUTTON_SELL   C'128,0,0'
#define COLOR_BUTTON_CLOSE  C'128,128,0'

//+------------------------------------------------------------------+
//| Global değişkenler                                              |
//+------------------------------------------------------------------+
string g_panelName = "AdvancedTradePanel";
int g_panelX = 50;
int g_panelY = 50;
bool g_isDragging = false;
int g_dragStartX = 0;
int g_dragStartY = 0;
datetime g_lastUpdate = 0;

// Panel verileri
struct PanelData
{
    int positionCount;
    double totalVolume;
    double totalProfit;
    double dailyPL;
    double profitableCount;
    double losingCount;
};

PanelData g_data;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    CreateTradePanel();
    UpdatePanelData();
    DrawPanel();
    
    Print("✅ Advanced Trade Panel başlatıldı");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    DeletePanel();
    Print("🔄 Advanced Trade Panel kapatıldı");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Her 2 saniyede bir güncelle
    if(TimeCurrent() - g_lastUpdate >= 2)
    {
        UpdatePanelData();
        DrawPanel();
        g_lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        HandleButtonClick(sparam);
    }
    else if(id == CHARTEVENT_MOUSE_MOVE)
    {
        HandleMouseMove((int)lparam, (int)dparam, (int)sparam);
    }
    else if(id == CHARTEVENT_OBJECT_DRAG)
    {
        if(StringFind(sparam, g_panelName) >= 0)
        {
            // Panel sürüklendiğinde pozisyonu güncelle
            g_panelX = (int)ObjectGetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE);
            g_panelY = (int)ObjectGetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE);
            RedrawPanel();
        }
    }
}

//+------------------------------------------------------------------+
//| Panel oluştur                                                   |
//+------------------------------------------------------------------+
void CreateTradePanel()
{
    // Ana panel arka planı
    ObjectCreate(0, g_panelName + "_Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE, g_panelX);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE, g_panelY);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YSIZE, PANEL_HEIGHT);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BGCOLOR, COLOR_BACKGROUND);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_COLOR, COLOR_BORDER);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BACK, false);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_SELECTABLE, true);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_SELECTED, false);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_HIDDEN, false);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_ZORDER, 1000);
    
    // Header
    ObjectCreate(0, g_panelName + "_Header", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XDISTANCE, g_panelX);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YDISTANCE, g_panelY);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YSIZE, HEADER_HEIGHT);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BGCOLOR, COLOR_HEADER);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BACK, false);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_ZORDER, 1001);
    
    CreateButtons();
}

//+------------------------------------------------------------------+
//| Butonları oluştur                                               |
//+------------------------------------------------------------------+
void CreateButtons()
{
    int btn_y = g_panelY + 120;
    int btn_width = (PANEL_WIDTH - 4 * MARGIN) / 3;
    
    // Karlı Kapat butonu
    CreateButton("BtnProfit", "💚 Karlı Kapat", g_panelX + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, COLOR_BUTTON_BUY);
    
    // Zarar Kapat butonu
    CreateButton("BtnLoss", "❤️ Zarar Kapat", g_panelX + MARGIN + btn_width + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, COLOR_BUTTON_SELL);
    
    // Hepsini Kapat butonu
    CreateButton("BtnAll", "🚫 Hepsini Kapat", g_panelX + MARGIN, btn_y + BUTTON_HEIGHT + MARGIN, 2 * btn_width + MARGIN, BUTTON_HEIGHT, COLOR_BUTTON_CLOSE);
}

//+------------------------------------------------------------------+
//| Buton oluştur                                                   |
//+------------------------------------------------------------------+
void CreateButton(string name, string text, int x, int y, int width, int height, color bgColor)
{
    string objName = g_panelName + "_" + name;
    
    // Buton arka planı
    ObjectCreate(0, objName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, objName, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, objName, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, objName, OBJPROP_BGCOLOR, bgColor);
    ObjectSetInteger(0, objName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, COLOR_BORDER);
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1002);
    
    // Buton metni
    ObjectCreate(0, objName + "_Text", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_XDISTANCE, x + width/2);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_YDISTANCE, y + height/2 - 6);
    ObjectSetString(0, objName + "_Text", OBJPROP_TEXT, text);
    ObjectSetString(0, objName + "_Text", OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, objName + "_Text", OBJPROP_FONTSIZE, 9);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_COLOR, COLOR_TEXT);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_ANCHOR, ANCHOR_CENTER);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_BACK, false);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_ZORDER, 1003);
}

//+------------------------------------------------------------------+
//| Panel verilerini güncelle                                       |
//+------------------------------------------------------------------+
void UpdatePanelData()
{
    g_data.positionCount = 0;
    g_data.totalVolume = 0;
    g_data.totalProfit = 0;
    g_data.profitableCount = 0;
    g_data.losingCount = 0;
    
    // Açık pozisyonları tara
    for(int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;
            
        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);
        
        g_data.positionCount++;
        g_data.totalVolume += volume;
        g_data.totalProfit += profit;
        
        if(profit > 0)
            g_data.profitableCount++;
        else if(profit < 0)
            g_data.losingCount++;
    }
    
    // Günlük P&L hesapla
    g_data.dailyPL = CalculateDailyPL();
}

//+------------------------------------------------------------------+
//| Günlük P&L hesapla                                             |
//+------------------------------------------------------------------+
double CalculateDailyPL()
{
    double dailyPL = 0;
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    // Bugünkü kapatılan işlemleri kontrol et
    HistorySelect(todayStart, TimeCurrent());
    
    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
            if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);
                
                dailyPL += (profit + commission + swap);
            }
        }
    }
    
    // Açık pozisyonların kar/zararını da ekle
    dailyPL += g_data.totalProfit;
    
    return dailyPL;
}

//+------------------------------------------------------------------+
//| Panel çiz                                                       |
//+------------------------------------------------------------------+
void DrawPanel()
{
    // Başlık
    CreateLabel("Title", "📊 İşlem Yönetim Paneli", g_panelX + PANEL_WIDTH/2, g_panelY + 15, COLOR_TEXT, 11, ANCHOR_CENTER);

    int y_pos = g_panelY + HEADER_HEIGHT + MARGIN;

    // Günlük P&L
    string dailyText = "💰 Günlük P&L: $" + DoubleToString(g_data.dailyPL, 2);
    color dailyColor = (g_data.dailyPL >= 0) ? COLOR_PROFIT : COLOR_LOSS;
    CreateLabel("DailyPL", dailyText, g_panelX + MARGIN, y_pos, dailyColor, 10);
    y_pos += 20;

    // Açık pozisyon sayısı
    string posText = "📈 Açık Pozisyon: " + IntegerToString(g_data.positionCount);
    CreateLabel("PositionCount", posText, g_panelX + MARGIN, y_pos, COLOR_TEXT, 9);
    y_pos += 18;

    // Toplam hacim
    string volText = "📊 Toplam Hacim: " + DoubleToString(g_data.totalVolume, 2);
    CreateLabel("TotalVolume", volText, g_panelX + MARGIN, y_pos, COLOR_TEXT, 9);
    y_pos += 18;

    // Toplam P&L
    string plText = "💵 Toplam P&L: $" + DoubleToString(g_data.totalProfit, 2);
    color plColor = (g_data.totalProfit >= 0) ? COLOR_PROFIT : COLOR_LOSS;
    CreateLabel("TotalPL", plText, g_panelX + MARGIN, y_pos, plColor, 10);
    y_pos += 18;

    // Karlı/Zararlı sayısı
    string countText = "📊 Karlı: " + IntegerToString((int)g_data.profitableCount) + " | Zararlı: " + IntegerToString((int)g_data.losingCount);
    CreateLabel("ProfitLossCount", countText, g_panelX + MARGIN, y_pos, COLOR_TEXT, 9);
    y_pos += 25;

    // Pozisyon listesi başlığı
    CreateLabel("ListTitle", "📋 Açık Pozisyonlar:", g_panelX + MARGIN, y_pos, clrYellow, 9);
    y_pos += 20;

    // Pozisyon listesi
    DrawPositionList(y_pos);
}

//+------------------------------------------------------------------+
//| Pozisyon listesini çiz                                          |
//+------------------------------------------------------------------+
void DrawPositionList(int start_y)
{
    int y_pos = start_y;
    int maxRows = 8; // Maksimum 8 pozisyon göster
    int rowCount = 0;

    // Önceki pozisyon label'larını temizle
    for(int i = 0; i < 20; i++)
    {
        ObjectDelete(0, g_panelName + "_Pos" + IntegerToString(i));
    }

    // Açık pozisyonları listele
    for(int i = 0; i < PositionsTotal() && rowCount < maxRows; i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);

        string typeStr = (type == POSITION_TYPE_BUY) ? "BUY" : "SELL";
        string profitStr = (profit >= 0) ? "+" + DoubleToString(profit, 2) : DoubleToString(profit, 2);
        color profitColor = (profit >= 0) ? COLOR_PROFIT : COLOR_LOSS;

        string posText = StringFormat("%s %s %.2f | $%s",
                                    typeStr,
                                    symbol,
                                    volume,
                                    profitStr);

        CreateLabel("Pos" + IntegerToString(rowCount), posText, g_panelX + MARGIN, y_pos, profitColor, 8);
        y_pos += ROW_HEIGHT;
        rowCount++;
    }

    // Eğer çok fazla pozisyon varsa "..." göster
    if(PositionsTotal() > maxRows)
    {
        string moreText = "... ve " + IntegerToString(PositionsTotal() - maxRows) + " pozisyon daha";
        CreateLabel("MorePositions", moreText, g_panelX + MARGIN, y_pos, clrGray, 8);
    }
}

//+------------------------------------------------------------------+
//| Label oluştur                                                   |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color clr, int fontSize, ENUM_ANCHOR_POINT anchor = ANCHOR_LEFT_UPPER)
{
    string objName = g_panelName + "_" + name;

    ObjectDelete(0, objName);
    ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetString(0, objName, OBJPROP_TEXT, text);
    ObjectSetString(0, objName, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, fontSize);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, objName, OBJPROP_ANCHOR, anchor);
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1002);
}

//+------------------------------------------------------------------+
//| Buton tıklama işleyici                                          |
//+------------------------------------------------------------------+
void HandleButtonClick(string objName)
{
    if(StringFind(objName, "BtnProfit") >= 0)
    {
        CloseProfitablePositions();
    }
    else if(StringFind(objName, "BtnLoss") >= 0)
    {
        CloseLosingPositions();
    }
    else if(StringFind(objName, "BtnAll") >= 0)
    {
        CloseAllPositions();
    }
}

//+------------------------------------------------------------------+
//| Mouse hareket işleyici                                          |
//+------------------------------------------------------------------+
void HandleMouseMove(int x, int y, int flags)
{
    // Sürükleme işlemi burada implement edilebilir
    // Şu an için basit sürükleme ObjectDrag ile yapılıyor
}

//+------------------------------------------------------------------+
//| Karlı pozisyonları kapat                                        |
//+------------------------------------------------------------------+
void CloseProfitablePositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("💚 Karlı pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double profit = PositionGetDouble(POSITION_PROFIT);
        if(profit > 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closedCount++;
                Print("✅ Karlı pozisyon kapatıldı: ", ticket, " Kar: $", DoubleToString(profit, 2));
            }
            else
            {
                Print("❌ Pozisyon kapatma hatası: ", ticket, " Hata: ", GetLastError());
            }
        }
    }

    Print("💚 Toplam ", closedCount, " karlı pozisyon kapatıldı");

    // Panel güncelle
    UpdatePanelData();
    DrawPanel();
}

//+------------------------------------------------------------------+
//| Zararlı pozisyonları kapat                                      |
//+------------------------------------------------------------------+
void CloseLosingPositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("❤️ Zararlı pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double profit = PositionGetDouble(POSITION_PROFIT);
        if(profit < 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closedCount++;
                Print("✅ Zararlı pozisyon kapatıldı: ", ticket, " Zarar: $", DoubleToString(profit, 2));
            }
            else
            {
                Print("❌ Pozisyon kapatma hatası: ", ticket, " Hata: ", GetLastError());
            }
        }
    }

    Print("❤️ Toplam ", closedCount, " zararlı pozisyon kapatıldı");

    // Panel güncelle
    UpdatePanelData();
    DrawPanel();
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("🚫 Tüm pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        double profit = PositionGetDouble(POSITION_PROFIT);

        if(trade.PositionClose(ticket))
        {
            closedCount++;
            Print("✅ Pozisyon kapatıldı: ", ticket, " P&L: $", DoubleToString(profit, 2));
        }
        else
        {
            Print("❌ Pozisyon kapatma hatası: ", ticket, " Hata: ", GetLastError());
        }
    }

    Print("🚫 Toplam ", closedCount, " pozisyon kapatıldı");

    // Panel güncelle
    UpdatePanelData();
    DrawPanel();
}

//+------------------------------------------------------------------+
//| Paneli yeniden çiz                                              |
//+------------------------------------------------------------------+
void RedrawPanel()
{
    // Tüm panel elementlerinin pozisyonunu güncelle
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XDISTANCE, g_panelX);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YDISTANCE, g_panelY);

    // Butonları yeniden konumlandır
    DeleteButtons();
    CreateButtons();

    // Panel içeriğini yeniden çiz
    DrawPanel();
}

//+------------------------------------------------------------------+
//| Butonları sil                                                   |
//+------------------------------------------------------------------+
void DeleteButtons()
{
    ObjectDelete(0, g_panelName + "_BtnProfit");
    ObjectDelete(0, g_panelName + "_BtnProfit_Text");
    ObjectDelete(0, g_panelName + "_BtnLoss");
    ObjectDelete(0, g_panelName + "_BtnLoss_Text");
    ObjectDelete(0, g_panelName + "_BtnAll");
    ObjectDelete(0, g_panelName + "_BtnAll_Text");
}

//+------------------------------------------------------------------+
//| Paneli sil                                                      |
//+------------------------------------------------------------------+
void DeletePanel()
{
    // Tüm panel objelerini sil
    ObjectsDeleteAll(0, g_panelName);
}
