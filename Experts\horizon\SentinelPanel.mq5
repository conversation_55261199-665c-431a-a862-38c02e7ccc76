//+------------------------------------------------------------------+
//| SentinelPanel.mq5 - Custom Indicator ile Popup Panel             |
//+------------------------------------------------------------------+
#property indicator_chart_window
#property indicator_buffers 0
#property indicator_plots 0

// MQL5 Standart Kütüphane include dosyaları
#include <Controls/Dialog.mqh>
#include <Controls/Button.mqh>
#include <Controls/Label.mqh>

// Panel için global nesneler
CDialog *sentinelDialog = NULL;
CLabel *lblTrend = NULL;
CLabel *lblSignal = NULL;
CLabel *lblBalance = NULL;
CLabel *lblDailyPL = NULL;
CLabel *lblOpenTrades = NULL;
CLabel *lblTotalSignals = NULL;
CLabel *lblApprovedSignals = NULL;
CLabel *lblSuccessRate = NULL;
CLabel *lblAutoTrade = NULL;
CButton *btnCloseProfit = NULL;
CButton *btnCloseAll = NULL;
bool dialogCreated = false;

// Panel bilgileri için global değişkenler
string g_currentTrend = "Yükleniyor...";
string g_lastSignal = "Henüz yok";
double g_balance = 0.0;
double g_dailyPL = 0.0;
int g_openTrades = 0;
int g_totalSignals = 0;
int g_approvedSignals = 0;
bool g_autoTrade = false;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                        |
//+------------------------------------------------------------------+
int OnInit()
  {
   CreateSentinelDialog();

   // Panel'i en üste getir
   SetPanelOnTop();

   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   CloseSentinelDialog();
  }

//+------------------------------------------------------------------+
//| Panel'i en üste getir                                           |
//+------------------------------------------------------------------+
void SetPanelOnTop()
  {
   if(!dialogCreated || !sentinelDialog) return;

   // Dialog'u en üste getir
   sentinelDialog.BringToTop();
   ChartRedraw();
  }
//+------------------------------------------------------------------+
//| Paneli oluşturur                                                |
//+------------------------------------------------------------------+
void CreateSentinelDialog()
  {
   if(dialogCreated) return;

   // Nesneleri oluştur
   sentinelDialog = new CDialog();
   lblTrend = new CLabel();
   lblSignal = new CLabel();
   lblBalance = new CLabel();
   lblDailyPL = new CLabel();
   lblOpenTrades = new CLabel();
   lblTotalSignals = new CLabel();
   lblApprovedSignals = new CLabel();
   lblSuccessRate = new CLabel();
   lblAutoTrade = new CLabel();
   btnCloseProfit = new CButton();
   btnCloseAll = new CButton();

   if(!sentinelDialog || !lblTrend || !lblSignal || !lblBalance || !lblDailyPL ||
      !lblOpenTrades || !lblTotalSignals || !lblApprovedSignals || !lblSuccessRate ||
      !lblAutoTrade || !btnCloseProfit || !btnCloseAll)
     {
      Print("Hata: Panel nesneleri oluşturulamadı!");
      return;
     }

   // Dialogu oluştur (ana grafik, ana pencere, konum ve boyut)
   if(!sentinelDialog.Create(0, "SentinelPopup", 0, 100, 100, 450, 420))
     {
      Print("Hata: Dialog oluşturulamadı!");
      return;
     }

   // Dialog özelliklerini ayarla
   sentinelDialog.Caption("🛡️ SENTINEL TRADING PANEL");

   // Panel bilgi etiketlerini oluştur
   CreatePanelLabels();

   // Butonları oluştur
   CreatePanelButtons();

   // Paneli görünür yap
   sentinelDialog.Visible(true);
   dialogCreated = true;
   ChartRedraw();

   Print("✅ Gelişmiş Sentinel Panel oluşturuldu");
  }

//+------------------------------------------------------------------+
//| Panel etiketlerini oluştur                                      |
//+------------------------------------------------------------------+
void CreatePanelLabels()
  {
   int yPos = 50;
   int lineHeight = 30;

   // Trend
   lblTrend.Create(0, "lblTrend", 0, 20, yPos, 400, yPos + 25);
   lblTrend.Text("Trend: " + g_currentTrend);
   lblTrend.Color(clrBlack);  // Siyah yazı
   lblTrend.Font("Arial");
   sentinelDialog.Add(lblTrend);
   yPos += lineHeight;

   // Son Sinyal
   lblSignal.Create(0, "lblSignal", 0, 20, yPos, 400, yPos + 25);
   lblSignal.Text("Son Sinyal: " + g_lastSignal);
   lblSignal.Color(clrBlack);  // Siyah yazı
   lblSignal.Font("Arial");
   sentinelDialog.Add(lblSignal);
   yPos += lineHeight;

   // Kasa
   lblBalance.Create(0, "lblBalance", 0, 20, yPos, 400, yPos + 25);
   lblBalance.Text("Kasa: " + DoubleToString(g_balance, 2) + " USD");
   lblBalance.Color(clrBlack);  // Siyah yazı
   lblBalance.Font("Arial");
   sentinelDialog.Add(lblBalance);
   yPos += lineHeight;

   // Günlük K/Z
   lblDailyPL.Create(0, "lblDailyPL", 0, 20, yPos, 400, yPos + 25);
   lblDailyPL.Text("Günlük K/Z: " + (g_dailyPL >= 0 ? "+" : "") + DoubleToString(g_dailyPL, 2) + " USD");
   lblDailyPL.Color(g_dailyPL >= 0 ? clrDarkGreen : clrDarkRed);  // Koyu renkler
   lblDailyPL.Font("Arial");
   sentinelDialog.Add(lblDailyPL);
   yPos += lineHeight;

   // Açık İşlem
   lblOpenTrades.Create(0, "lblOpenTrades", 0, 20, yPos, 400, yPos + 25);
   lblOpenTrades.Text("Açık İşlem: " + IntegerToString(g_openTrades));
   lblOpenTrades.Color(clrBlack);  // Siyah yazı
   lblOpenTrades.Font("Arial");
   sentinelDialog.Add(lblOpenTrades);
   yPos += lineHeight;

   // Toplam Sinyal
   lblTotalSignals.Create(0, "lblTotalSignals", 0, 20, yPos, 400, yPos + 25);
   lblTotalSignals.Text("Toplam Sinyal: " + IntegerToString(g_totalSignals));
   lblTotalSignals.Color(clrBlack);  // Siyah yazı
   lblTotalSignals.Font("Arial");
   sentinelDialog.Add(lblTotalSignals);
   yPos += lineHeight;

   // Onaylanan Sinyal
   lblApprovedSignals.Create(0, "lblApprovedSignals", 0, 20, yPos, 400, yPos + 25);
   lblApprovedSignals.Text("Onaylanan: " + IntegerToString(g_approvedSignals));
   lblApprovedSignals.Color(clrBlack);  // Siyah yazı
   lblApprovedSignals.Font("Arial");
   sentinelDialog.Add(lblApprovedSignals);
   yPos += lineHeight;

   // Başarı Oranı
   lblSuccessRate.Create(0, "lblSuccessRate", 0, 20, yPos, 400, yPos + 25);
   double successRate = (g_totalSignals > 0) ? (double)g_approvedSignals / g_totalSignals * 100 : 0;
   lblSuccessRate.Text("Başarı Oranı: " + DoubleToString(successRate, 1) + "%");
   lblSuccessRate.Color(clrBlack);  // Siyah yazı
   lblSuccessRate.Font("Arial");
   sentinelDialog.Add(lblSuccessRate);
   yPos += lineHeight;

   // Auto Trade
   lblAutoTrade.Create(0, "lblAutoTrade", 0, 20, yPos, 400, yPos + 25);
   lblAutoTrade.Text("Auto Trade: " + (g_autoTrade ? "Açık" : "Kapalı"));
   lblAutoTrade.Color(g_autoTrade ? clrDarkGreen : clrDarkRed);  // Koyu renkler
   lblAutoTrade.Font("Arial");
   sentinelDialog.Add(lblAutoTrade);
  }

//+------------------------------------------------------------------+
//| Panel butonlarını oluştur                                       |
//+------------------------------------------------------------------+
void CreatePanelButtons()
  {
   // Karda Olanları Kapat Butonu
   btnCloseProfit.Create(0, "SENTINEL_BtnCloseProfit", 0, 20, 350, 200, 380);
   btnCloseProfit.Text("Karda Olanları Kapat");
   btnCloseProfit.Color(clrWhite);  // Beyaz yazı
   btnCloseProfit.ColorBackground(clrDarkGreen);  // Yeşil arka plan
   btnCloseProfit.Font("Arial");
   sentinelDialog.Add(btnCloseProfit);

   // Tümünü Kapat Butonu
   btnCloseAll.Create(0, "SENTINEL_BtnCloseAll", 0, 220, 350, 400, 380);
   btnCloseAll.Text("Tümünü Kapat");
   btnCloseAll.Color(clrWhite);  // Beyaz yazı
   btnCloseAll.ColorBackground(clrDarkRed);  // Kırmızı arka plan
   btnCloseAll.Font("Arial");
   sentinelDialog.Add(btnCloseAll);
  }

//+------------------------------------------------------------------+
//| Paneli kapatır                                                  |
//+------------------------------------------------------------------+
void CloseSentinelDialog()
  {
   if(dialogCreated && sentinelDialog != NULL)
     {
      sentinelDialog.Destroy();
      delete sentinelDialog;
      delete lblTrend;
      delete lblSignal;
      delete lblBalance;
      delete lblDailyPL;
      delete lblOpenTrades;
      delete lblTotalSignals;
      delete lblApprovedSignals;
      delete lblSuccessRate;
      delete lblAutoTrade;
      delete btnCloseProfit;
      delete btnCloseAll;

      sentinelDialog = NULL;
      lblTrend = NULL;
      lblSignal = NULL;
      lblBalance = NULL;
      lblDailyPL = NULL;
      lblOpenTrades = NULL;
      lblTotalSignals = NULL;
      lblApprovedSignals = NULL;
      lblSuccessRate = NULL;
      lblAutoTrade = NULL;
      btnCloseProfit = NULL;
      btnCloseAll = NULL;
      dialogCreated = false;
      ChartRedraw();
     }
  }
//+------------------------------------------------------------------+
//| Chart event handler                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,const long &lparam,const double &dparam,const string &sparam)
  {
   // Panel oluşturulmamışsa çık
   if(!dialogCreated || !sentinelDialog) return;

   // Buton tıklamalarını işle
   if(id == CHARTEVENT_OBJECT_CLICK)
     {
      if(sparam == "SENTINEL_BtnCloseProfit")
        {
         Print("✅ SentinelPanel: Karda Olanları Kapat butonu tıklandı");
         // Sentinel.mq5'e mesaj gönder (GlobalVariable veya dosya ile)
         GlobalVariableSet("SENTINEL_CloseProfit", 1);
         return;
        }

      if(sparam == "SENTINEL_BtnCloseAll")
        {
         Print("✅ SentinelPanel: Tümünü Kapat butonu tıklandı");
         // Sentinel.mq5'e mesaj gönder (GlobalVariable veya dosya ile)
         GlobalVariableSet("SENTINEL_CloseAll", 1);
         return;
        }
     }

   // Diğer eventler için panelin event handler'ını çağır
   sentinelDialog.OnEvent(id, lparam, dparam, sparam);
  }

//+------------------------------------------------------------------+
//| Panel bilgilerini güncelle                                      |
//+------------------------------------------------------------------+
void UpdatePanelData()
  {
   if(!dialogCreated) return;

   // GlobalVariable'lardan Sentinel.mq5'ten veri oku
   if(GlobalVariableCheck("SENTINEL_Balance"))
     g_balance = GlobalVariableGet("SENTINEL_Balance");
   else
     g_balance = AccountInfoDouble(ACCOUNT_BALANCE);

   if(GlobalVariableCheck("SENTINEL_DailyPL"))
     g_dailyPL = GlobalVariableGet("SENTINEL_DailyPL");
   else
     g_dailyPL = 0.0;

   if(GlobalVariableCheck("SENTINEL_OpenTrades"))
     g_openTrades = (int)GlobalVariableGet("SENTINEL_OpenTrades");
   else
     g_openTrades = PositionsTotal();

   if(GlobalVariableCheck("SENTINEL_TotalSignals"))
     g_totalSignals = (int)GlobalVariableGet("SENTINEL_TotalSignals");
   else
     g_totalSignals = 0;

   if(GlobalVariableCheck("SENTINEL_ApprovedSignals"))
     g_approvedSignals = (int)GlobalVariableGet("SENTINEL_ApprovedSignals");
   else
     g_approvedSignals = 0;

   if(GlobalVariableCheck("SENTINEL_AutoTrade"))
     g_autoTrade = (GlobalVariableGet("SENTINEL_AutoTrade") == 1);
   else
     g_autoTrade = false;

   // Trend ve sinyal bilgileri (string olduğu için şu an sabit)
   g_currentTrend = "Yükleniyor...";
   g_lastSignal = "Henüz yok";

   // Etiketleri güncelle
   if(lblTrend)
     {
      lblTrend.Text("Trend: " + g_currentTrend);
      lblTrend.Color(clrBlack);
     }
   if(lblSignal)
     {
      lblSignal.Text("Son Sinyal: " + g_lastSignal);
      lblSignal.Color(clrBlack);
     }
   if(lblBalance)
     {
      lblBalance.Text("Kasa: " + DoubleToString(g_balance, 2) + " USD");
      lblBalance.Color(clrBlack);
     }
   if(lblDailyPL)
     {
      lblDailyPL.Text("Günlük K/Z: " + (g_dailyPL >= 0 ? "+" : "") + DoubleToString(g_dailyPL, 2) + " USD");
      lblDailyPL.Color(g_dailyPL >= 0 ? clrDarkGreen : clrDarkRed);
     }
   if(lblOpenTrades)
     {
      lblOpenTrades.Text("Açık İşlem: " + IntegerToString(g_openTrades));
      lblOpenTrades.Color(clrBlack);
     }
   if(lblTotalSignals)
     {
      lblTotalSignals.Text("Toplam Sinyal: " + IntegerToString(g_totalSignals));
      lblTotalSignals.Color(clrBlack);
     }
   if(lblApprovedSignals)
     {
      lblApprovedSignals.Text("Onaylanan: " + IntegerToString(g_approvedSignals));
      lblApprovedSignals.Color(clrBlack);
     }
   if(lblSuccessRate)
     {
      double successRate = (g_totalSignals > 0) ? (double)g_approvedSignals / g_totalSignals * 100 : 0;
      lblSuccessRate.Text("Başarı Oranı: " + DoubleToString(successRate, 1) + "%");
      lblSuccessRate.Color(clrBlack);
     }
   if(lblAutoTrade)
     {
      lblAutoTrade.Text("Auto Trade: " + (g_autoTrade ? "Açık" : "Kapalı"));
      lblAutoTrade.Color(g_autoTrade ? clrDarkGreen : clrDarkRed);
     }
  }

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   // Panel bilgilerini güncelle (her tick'te)
   static int tickCounter = 0;
   static int zOrderCounter = 0;
   tickCounter++;
   zOrderCounter++;

   if(tickCounter >= 10) // Her 10 tick'te bir güncelle
     {
      UpdatePanelData();
      tickCounter = 0;
     }

   if(zOrderCounter >= 50) // Her 50 tick'te bir en üste getir
     {
      SetPanelOnTop();
      zOrderCounter = 0;
     }

   return(rates_total);
  }
//+------------------------------------------------------------------+ 