//+------------------------------------------------------------------+
//|                                           BuySellIndicator_MT5.mq5 |
//|                        Copyright 2025, Emre USUN - Horizon Team |
//|                    Buy Sell Indicator - MQ4'den MQ5'e çevrildi  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Emre USUN - Horizon Team"
#property link      "https://github.com/emreusun87"
#property version   "1.0"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- Plot ayarları
#property indicator_label1  "BUY"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  1

#property indicator_label2  "SELL"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrOrangeRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  1

//--- Input parameters
input group "=== Bollinger Bands Ayarları ==="
input int                   bb_period = 20;                  // BB Periyodu
input int                   bb_bands_shift = 0;              // BB Shift
input double                bb_deviation = 2.0;              // BB Sapma
input ENUM_APPLIED_PRICE    bb_applied_price = PRICE_CLOSE;  // BB Uygulanan Fiyat

input group "=== Moving Average Ayarları ==="
input int                   ma_period = 50;                  // MA Periyodu
input int                   ma_shift = 0;                    // MA Shift
input ENUM_MA_METHOD        ma_method = MODE_EMA;            // MA Metodu
input ENUM_APPLIED_PRICE    ma_applied_price = PRICE_CLOSE;  // MA Uygulanan Fiyat

input group "=== Bildirim Ayarları ==="
input bool                  notifications = true;            // Bildirimleri Etkinleştir
input bool                  desktop_notifications = true;    // Masaüstü Bildirimleri
input bool                  email_notifications = false;     // Email Bildirimleri
input bool                  push_notifications = false;      // Push Bildirimleri

//--- Indicator buffers
double BuyArrowBuffer[];
double SellArrowBuffer[];

//--- Global variables
int bb_handle;                    // Bollinger Bands handle
int ma_handle;                    // Moving Average handle
datetime last_alert_time = 0;     // Son alert zamanı

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Buffer ayarları
    SetIndexBuffer(0, BuyArrowBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SellArrowBuffer, INDICATOR_DATA);
    
    // Arrow kodları
    PlotIndexSetInteger(0, PLOT_ARROW, 217);  // BUY arrow
    PlotIndexSetInteger(1, PLOT_ARROW, 218);  // SELL arrow
    
    // Buffer'ları başlat
    ArraySetAsSeries(BuyArrowBuffer, true);
    ArraySetAsSeries(SellArrowBuffer, true);
    
    // Bollinger Bands handle oluştur
    bb_handle = iBands(_Symbol, _Period, bb_period, bb_bands_shift, bb_deviation, bb_applied_price);
    if(bb_handle == INVALID_HANDLE)
    {
        Print("❌ Bollinger Bands handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Moving Average handle oluştur
    ma_handle = iMA(_Symbol, _Period, ma_period, ma_shift, ma_method, ma_applied_price);
    if(ma_handle == INVALID_HANDLE)
    {
        Print("❌ Moving Average handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Indicator ismi
    IndicatorSetString(INDICATOR_SHORTNAME, "BuySell(BB:" + IntegerToString(bb_period) + ",MA:" + IntegerToString(ma_period) + ")");
    
    Print("📊 Buy Sell Indicator MT5 başlatıldı");
    Print("   BB Periyodu: ", bb_period, ", Sapma: ", bb_deviation);
    Print("   MA Periyodu: ", ma_period, ", Metod: ", EnumToString(ma_method));
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // Yeterli veri var mı kontrol et
    if(rates_total < bb_period + ma_period + 10) return 0;
    
    // Handle'lar hazır mı kontrol et
    if(BarsCalculated(bb_handle) < rates_total || BarsCalculated(ma_handle) < rates_total)
        return 0;
    
    // Buffer'ları hazırla
    double bb_upper[], bb_lower[], bb_middle[];
    double ma_values[];
    
    ArraySetAsSeries(bb_upper, true);
    ArraySetAsSeries(bb_lower, true);
    ArraySetAsSeries(bb_middle, true);
    ArraySetAsSeries(ma_values, true);
    
    // Veri kopyala
    int copy_bars = rates_total - prev_calculated + 10;
    if(prev_calculated == 0) copy_bars = rates_total;
    
    if(CopyBuffer(bb_handle, 1, 0, copy_bars, bb_upper) <= 0) return 0;   // Upper band
    if(CopyBuffer(bb_handle, 2, 0, copy_bars, bb_lower) <= 0) return 0;   // Lower band
    if(CopyBuffer(bb_handle, 0, 0, copy_bars, bb_middle) <= 0) return 0;  // Middle band
    if(CopyBuffer(ma_handle, 0, 0, copy_bars, ma_values) <= 0) return 0;  // MA values
    
    // Array'leri time series olarak ayarla
    ArraySetAsSeries(time, true);
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    // Hesaplama başlangıç noktası
    int start = prev_calculated;
    if(start == 0) start = bb_period + ma_period + 10;
    
    // Ana hesaplama döngüsü
    for(int i = start; i < rates_total - 1; i++)
    {
        // Buffer index'leri (time series)
        int current_index = rates_total - 1 - i;
        int prev_index = current_index + 1;
        
        // Buffer'ları temizle
        BuyArrowBuffer[current_index] = EMPTY_VALUE;
        SellArrowBuffer[current_index] = EMPTY_VALUE;
        
        // Geçmiş ve şimdiki değerler
        double ma_current = ma_values[current_index];
        double ma_prev = ma_values[prev_index];
        
        double bb_upper_current = bb_upper[current_index];
        double bb_lower_current = bb_lower[current_index];
        double bb_upper_prev = bb_upper[prev_index];
        double bb_lower_prev = bb_lower[prev_index];
        
        // BUY Sinyali: MA önceden BB alt bandının üstündeydi, şimdi altına düştü
        if(ma_prev > bb_lower_prev && ma_current < bb_lower_current)
        {
            BuyArrowBuffer[current_index] = low[current_index] - GetPointDistance();
            
            // Alert (sadece en son bar için)
            if(i == rates_total - 2 && time[current_index] != last_alert_time)
            {
                SendAlert("BUY", close[current_index]);
                last_alert_time = time[current_index];
            }
        }
        
        // SELL Sinyali: MA önceden BB üst bandının altındaydı, şimdi üstüne çıktı
        if(ma_prev < bb_upper_prev && ma_current > bb_upper_current)
        {
            SellArrowBuffer[current_index] = high[current_index] + GetPointDistance();
            
            // Alert (sadece en son bar için)
            if(i == rates_total - 2 && time[current_index] != last_alert_time)
            {
                SendAlert("SELL", close[current_index]);
                last_alert_time = time[current_index];
            }
        }
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| Alert gönder                                                     |
//+------------------------------------------------------------------+
void SendAlert(string signal_type, double price)
{
    if(!notifications) return;
    
    string message = _Symbol + " " + GetTimeFrameString(_Period) + " " + signal_type + 
                    " @ " + DoubleToString(price, _Digits);
    
    if(desktop_notifications)
        Alert(message);
        
    if(push_notifications)
        SendNotification(message);
        
    if(email_notifications)
        SendMail("MetaTrader Buy/Sell Signal", message);
        
    Print("🔔 ", signal_type, " sinyali: ", message);
}

//+------------------------------------------------------------------+
//| Point mesafesi hesapla                                           |
//+------------------------------------------------------------------+
double GetPointDistance()
{
    switch(_Period)
    {
        case PERIOD_M1:  return 5 * _Point;
        case PERIOD_M5:  return 10 * _Point;
        case PERIOD_M15: return 22 * _Point;
        case PERIOD_M30: return 44 * _Point;
        case PERIOD_H1:  return 80 * _Point;
        case PERIOD_H4:  return 120 * _Point;
        case PERIOD_D1:  return 170 * _Point;
        case PERIOD_W1:  return 500 * _Point;
        case PERIOD_MN1: return 900 * _Point;
        default:         return 20 * _Point;
    }
}

//+------------------------------------------------------------------+
//| Timeframe string'e çevir                                         |
//+------------------------------------------------------------------+
string GetTimeFrameString(ENUM_TIMEFRAMES period)
{
    switch(period)
    {
        case PERIOD_M1:  return "M1";
        case PERIOD_M5:  return "M5";
        case PERIOD_M15: return "M15";
        case PERIOD_M30: return "M30";
        case PERIOD_H1:  return "H1";
        case PERIOD_H4:  return "H4";
        case PERIOD_D1:  return "D1";
        case PERIOD_W1:  return "W1";
        case PERIOD_MN1: return "MN1";
        default:         return IntegerToString(period);
    }
}

//+------------------------------------------------------------------+
//| Indicator temizleme                                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Handle'ları serbest bırak
    if(bb_handle != INVALID_HANDLE) IndicatorRelease(bb_handle);
    if(ma_handle != INVALID_HANDLE) IndicatorRelease(ma_handle);
    
    Print("📊 Buy Sell Indicator MT5 durduruldu");
}
