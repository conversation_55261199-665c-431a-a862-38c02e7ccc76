#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XAUUSD M5 Karar Matrisi Analizi
CSV verilerinden BUY/SELL şart şablonları oluşturur
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_data(file_path):
    """CSV dosyasını yükle ve temizle"""
    print("📊 CSV dosyası yükleniyor...")
    
    # CSV'yi oku
    df = pd.read_csv(file_path, sep='\t')
    
    # Sütun isimlerini temizle
    df.columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'TickVol', 'Vol', 'Spread']
    
    # İlk satırı (header) kaldır
    df = df.drop(0).reset_index(drop=True)
    
    # Veri tiplerini düzelt
    df['Open'] = pd.to_numeric(df['Open'])
    df['High'] = pd.to_numeric(df['High'])
    df['Low'] = pd.to_numeric(df['Low'])
    df['Close'] = pd.to_numeric(df['Close'])
    df['TickVol'] = pd.to_numeric(df['TickVol'])
    df['Spread'] = pd.to_numeric(df['Spread'])
    
    # DateTime oluştur
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
    
    print(f"✅ {len(df)} bar veri yüklendi")
    print(f"📅 Tarih aralığı: {df['DateTime'].min()} - {df['DateTime'].max()}")
    
    return df

def calculate_metrics(df):
    """Analiz metriklerini hesapla"""
    print("🔢 Metrikler hesaplanıyor...")
    
    # Bar büyüklüğü (pip cinsinden)
    df['BarSize'] = (df['High'] - df['Low']) * 10  # XAUUSD için 1 pip = 0.1
    
    # Kapanış pozisyonu (bar içinde %)
    df['ClosePosition'] = (df['Close'] - df['Low']) / (df['High'] - df['Low']) * 100
    df['ClosePosition'] = df['ClosePosition'].fillna(50)  # Doji barlar için
    
    # Bar yönü
    df['BarDirection'] = np.where(df['Close'] > df['Open'], 'UP', 
                         np.where(df['Close'] < df['Open'], 'DOWN', 'DOJI'))
    
    # Sonraki bar yönü (hedef)
    df['NextBarDirection'] = df['BarDirection'].shift(-1)
    
    # Sonraki bar hareket (pip)
    df['NextBarMove'] = (df['Close'].shift(-1) - df['Close']) * 10
    
    # Ardışık bar sayısı
    df['ConsecutiveBars'] = 0
    for i in range(1, len(df)):
        if df.loc[i, 'BarDirection'] == df.loc[i-1, 'BarDirection']:
            df.loc[i, 'ConsecutiveBars'] = df.loc[i-1, 'ConsecutiveBars'] + 1
        else:
            df.loc[i, 'ConsecutiveBars'] = 1
    
    # Volatilite (son 5 bar ortalama)
    df['Volatility5'] = df['BarSize'].rolling(5).mean()
    
    # Spread kategorisi
    df['SpreadCategory'] = pd.cut(df['Spread'], 
                                 bins=[0, 10, 15, 25, 100], 
                                 labels=['Low', 'Normal', 'High', 'VeryHigh'])
    
    # Saat kategorisi
    df['Hour'] = df['DateTime'].dt.hour
    df['HourCategory'] = pd.cut(df['Hour'], 
                               bins=[0, 6, 12, 18, 24], 
                               labels=['Night', 'Morning', 'Afternoon', 'Evening'])
    
    print("✅ Metrikler hesaplandı")
    return df

def analyze_patterns(df):
    """Karar matrisi analizi"""
    print("🎯 Karar matrisi oluşturuluyor...")
    
    results = {}
    
    # 1. Bar Büyüklüğü Analizi
    print("\n📏 Bar Büyüklüğü Analizi:")
    bar_size_bins = [0, 5, 15, 30, 100]
    bar_size_labels = ['VerySmall(0-5)', 'Small(5-15)', 'Medium(15-30)', 'Large(30+)']
    df['BarSizeCategory'] = pd.cut(df['BarSize'], bins=bar_size_bins, labels=bar_size_labels)
    
    bar_analysis = df.groupby('BarSizeCategory')['NextBarDirection'].value_counts(normalize=True).unstack(fill_value=0)
    results['bar_size'] = bar_analysis
    print(bar_analysis.round(3))
    
    # 2. Kapanış Pozisyonu Analizi
    print("\n📍 Kapanış Pozisyonu Analizi:")
    close_pos_bins = [0, 25, 75, 100]
    close_pos_labels = ['Lower25%', 'Middle50%', 'Upper25%']
    df['ClosePosCategory'] = pd.cut(df['ClosePosition'], bins=close_pos_bins, labels=close_pos_labels)
    
    close_analysis = df.groupby('ClosePosCategory')['NextBarDirection'].value_counts(normalize=True).unstack(fill_value=0)
    results['close_position'] = close_analysis
    print(close_analysis.round(3))
    
    # 3. Ardışık Bar Analizi
    print("\n🔄 Ardışık Bar Analizi:")
    consecutive_analysis = df[df['ConsecutiveBars'] >= 2].groupby(['BarDirection', 'ConsecutiveBars'])['NextBarDirection'].value_counts(normalize=True).unstack(fill_value=0)
    results['consecutive'] = consecutive_analysis
    print(consecutive_analysis.round(3))
    
    # 4. Volatilite Analizi
    print("\n📈 Volatilite Analizi:")
    vol_bins = [0, 8, 15, 25, 100]
    vol_labels = ['Low(0-8)', 'Normal(8-15)', 'High(15-25)', 'VeryHigh(25+)']
    df['VolCategory'] = pd.cut(df['Volatility5'], bins=vol_bins, labels=vol_labels)
    
    vol_analysis = df.groupby('VolCategory')['NextBarDirection'].value_counts(normalize=True).unstack(fill_value=0)
    results['volatility'] = vol_analysis
    print(vol_analysis.round(3))
    
    # 5. Spread Analizi
    print("\n💰 Spread Analizi:")
    spread_analysis = df.groupby('SpreadCategory')['NextBarDirection'].value_counts(normalize=True).unstack(fill_value=0)
    results['spread'] = spread_analysis
    print(spread_analysis.round(3))
    
    # 6. Saat Analizi
    print("\n🕐 Saat Analizi:")
    hour_analysis = df.groupby('HourCategory')['NextBarDirection'].value_counts(normalize=True).unstack(fill_value=0)
    results['hour'] = hour_analysis
    print(hour_analysis.round(3))
    
    return results, df

def generate_templates(df):
    """Şart şablonları oluştur"""
    print("\n🎯 Şart Şablonları Oluşturuluyor...")
    
    templates = []
    
    # BUY Template 1: Oversold Reversal
    buy1_condition = (
        (df['BarSizeCategory'] == 'Small(5-15)') &
        (df['ClosePosCategory'] == 'Lower25%') &
        (df['BarDirection'] == 'DOWN') &
        (df['ConsecutiveBars'] >= 2) &
        (df['VolCategory'] == 'Normal(8-15)')
    )
    
    buy1_success = df[buy1_condition]['NextBarDirection'] == 'UP'
    buy1_rate = buy1_success.sum() / len(buy1_success) * 100 if len(buy1_success) > 0 else 0
    buy1_avg_move = df[buy1_condition]['NextBarMove'].mean() if len(buy1_success) > 0 else 0
    
    templates.append({
        'name': 'BUY_OversoldReversal',
        'conditions': [
            'Bar büyüklüğü 5-15 pip',
            'Kapanış alt %25\'te',
            'Kırmızı bar',
            '2+ ardışık düşüş',
            'Normal volatilite'
        ],
        'success_rate': buy1_rate,
        'avg_move': buy1_avg_move,
        'sample_size': len(buy1_success)
    })
    
    # SELL Template 1: Overbought Reversal
    sell1_condition = (
        (df['BarSizeCategory'] == 'Medium(15-30)') &
        (df['ClosePosCategory'] == 'Upper25%') &
        (df['BarDirection'] == 'UP') &
        (df['ConsecutiveBars'] >= 2) &
        (df['VolCategory'] == 'High(15-25)')
    )
    
    sell1_success = df[sell1_condition]['NextBarDirection'] == 'DOWN'
    sell1_rate = sell1_success.sum() / len(sell1_success) * 100 if len(sell1_success) > 0 else 0
    sell1_avg_move = df[sell1_condition]['NextBarMove'].mean() if len(sell1_success) > 0 else 0
    
    templates.append({
        'name': 'SELL_OverboughtReversal',
        'conditions': [
            'Bar büyüklüğü 15-30 pip',
            'Kapanış üst %25\'te',
            'Yeşil bar',
            '2+ ardışık yükseliş',
            'Yüksek volatilite'
        ],
        'success_rate': sell1_rate,
        'avg_move': sell1_avg_move,
        'sample_size': len(sell1_success)
    })
    
    return templates

def main():
    """Ana analiz fonksiyonu"""
    print("🚀 XAUUSD M5 Karar Matrisi Analizi Başlıyor...")
    
    # Veriyi yükle
    df = load_data('XAUUSD_M5_202501020100_202507170555.csv')
    
    # Metrikleri hesapla
    df = calculate_metrics(df)
    
    # Analiz yap
    results, df = analyze_patterns(df)
    
    # Şablonları oluştur
    templates = generate_templates(df)
    
    # Sonuçları yazdır
    print("\n" + "="*60)
    print("📋 ŞART ŞABLONLARI")
    print("="*60)
    
    for template in templates:
        print(f"\n🎯 {template['name']}:")
        print(f"   Başarı Oranı: {template['success_rate']:.1f}%")
        print(f"   Ortalama Hareket: {template['avg_move']:.1f} pip")
        print(f"   Test Sayısı: {template['sample_size']}")
        print("   Koşullar:")
        for condition in template['conditions']:
            print(f"   ✓ {condition}")
    
    print(f"\n✅ Analiz tamamlandı!")
    print(f"📊 Toplam {len(df)} bar analiz edildi")

if __name__ == "__main__":
    main()
