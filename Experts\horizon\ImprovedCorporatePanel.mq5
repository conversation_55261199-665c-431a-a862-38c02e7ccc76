//+------------------------------------------------------------------+
//| ImprovedCorporatePanel.mq5 - Dü<PERSON>tilmiş Kurumsal Panel        |
//| <PERSON>ü<PERSON><PERSON>l hizalama + Elle düzenlenebilir inputlar + G<PERSON><PERSON> butonlar|
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Düzeltilmiş Kurumsal İşlem Paneli"

#include <Trade\Trade.mqh>
#include <Controls\Dialog.mqh>
#include <Controls\Edit.mqh>
#include <Controls\Button.mqh>
#include <Controls\Label.mqh>

//+------------------------------------------------------------------+
//| Panel ayarları                                                  |
//+------------------------------------------------------------------+
input group "=== Panel Ayarları ==="
input int StartX = 50;              // Başlangıç X pozisyonu
input int StartY = 50;              // Başlangıç Y pozisyonu

input group "=== İşlem Ayarları ==="
input double DefaultLot = 0.1;      // Varsayılan Lot
input int DefaultSL = 50;           // Varsayılan SL (pip)
input int DefaultTP = 100;          // Varsayılan TP (pip)

input group "=== İndikatör Eşikleri ==="
input double RSI_Oversold = 30;    // RSI Aşırı Satım
input double RSI_Overbought = 70;  // RSI Aşırı Alım
input double ATR_High = 0.002;     // ATR Yüksek Eşik
input int MA_Period = 20;          // MA Periyodu

//+------------------------------------------------------------------+
//| Panel boyutları - Düzeltilmiş                                  |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     440
#define PANEL_HEIGHT    400
#define COLUMN_WIDTH    200
#define SECTION_HEIGHT  120
#define INPUT_WIDTH     80
#define BUTTON_WIDTH    90
#define BUTTON_HEIGHT   30
#define MARGIN          15
#define SPACING         10

//+------------------------------------------------------------------+
//| Kurumsal Renkler                                                |
//+------------------------------------------------------------------+
#define COLOR_TITLE_BG      clrNavy          // Koyu mavi başlık
#define COLOR_PANEL_BG      clrWhiteSmoke   // Çok açık gri arka plan
#define COLOR_SECTION_BG    clrLightGray    // Bölüm arka planı
#define COLOR_INPUT_BG      clrWhite        // Input arka planı
#define COLOR_BUTTON_BUY    clrForestGreen  // Güzel yeşil
#define COLOR_BUTTON_SELL   clrCrimson      // Güzel kırmızı
#define COLOR_BUTTON_MANAGE clrSteelBlue    // Mavi tonları
#define COLOR_TEXT_NORMAL   clrDarkSlateGray // Koyu gri metin
#define COLOR_TEXT_TITLE    clrWhite        // Beyaz başlık
#define COLOR_BORDER        clrSilver       // Açık gri kenarlık

//+------------------------------------------------------------------+
//| CorporateTradeDialog sınıfı                                     |
//+------------------------------------------------------------------+
class CorporateTradeDialog : public CDialog
{
private:
    // UI Elementleri - Sol Sütun
    CLabel            m_lblIndicatorTitle;
    CLabel            m_lblRSI;
    CLabel            m_lblRSIValue;
    CLabel            m_lblMA;
    CLabel            m_lblMAValue;
    CLabel            m_lblATR;
    CLabel            m_lblATRValue;
    
    CLabel            m_lblPositionTitle;
    CLabel            m_lblPosCount;
    CLabel            m_lblTotalPL;
    CLabel            m_lblDailyPL;
    CLabel            m_lblTotalVol;
    CLabel            m_lblProfitCount;
    
    // UI Elementleri - Sağ Sütun
    CLabel            m_lblTradeTitle;
    CLabel            m_lblLot;
    CEdit             m_editLot;
    CLabel            m_lblSL;
    CEdit             m_editSL;
    CLabel            m_lblTP;
    CEdit             m_editTP;
    
    CButton           m_btnBuy;
    CButton           m_btnSell;
    
    CLabel            m_lblManageTitle;
    CButton           m_btnCloseProfit;
    CButton           m_btnCloseLoss;
    CButton           m_btnCloseAll;

    // Panel kontrolleri
    CButton           m_btnMinimize;
    CButton           m_btnClose;

    // Veriler
    datetime          m_lastUpdate;
    double            g_rsiValue;
    double            g_maValue;
    double            g_atrValue;
    bool              m_isMinimized;
    
public:
    CorporateTradeDialog();
    ~CorporateTradeDialog();
    
    virtual bool      Create(const long chart, const string name, const int subwin, const int x1, const int y1);
    virtual void      OnTick();
    virtual bool      OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam);
    
    void              UpdateIndicators();
    void              UpdatePositionData();
    void              UpdateIndicatorDisplay();
    void              CreateLeftColumn();
    void              CreateRightColumn();
    void              CreatePanelControls();
    void              ToggleMinimize();
    void              ShowAllControls(bool show);

    // İşlem fonksiyonları
    void              ExecuteBuyOrder();
    void              ExecuteSellOrder();
    void              CloseProfitablePositions();
    void              CloseLosingPositions();
    void              CloseAllPositions();
    double            CalculateDailyPL();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CorporateTradeDialog::CorporateTradeDialog()
{
    m_lastUpdate = 0;
    g_rsiValue = 0;
    g_maValue = 0;
    g_atrValue = 0;
    m_isMinimized = false;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CorporateTradeDialog::~CorporateTradeDialog()
{
}

//+------------------------------------------------------------------+
//| Panel oluştur                                                   |
//+------------------------------------------------------------------+
bool CorporateTradeDialog::Create(const long chart, const string name, const int subwin, const int x1, const int y1)
{
    if(!CDialog::Create(chart, name, subwin, x1, y1, x1 + PANEL_WIDTH, y1 + PANEL_HEIGHT))
        return false;
    
    // Panel özelliklerini ayarla
    SetInteger(OBJPROP_BGCOLOR, COLOR_PANEL_BG);
    SetInteger(OBJPROP_BORDER_TYPE, BORDER_RAISED);
    SetInteger(OBJPROP_COLOR, COLOR_BORDER);

    // Başlık çubuğunu aktif et
    Caption("📊 CORPORATE TRADE PANEL");

    // Panel kontrol butonları oluştur
    CreatePanelControls();

    CreateLeftColumn();
    CreateRightColumn();
    
    UpdateIndicators();
    UpdatePositionData();
    
    return true;
}

//+------------------------------------------------------------------+
//| Panel kontrol butonları oluştur                                 |
//+------------------------------------------------------------------+
void CorporateTradeDialog::CreatePanelControls()
{
    // Minimize butonu
    m_btnMinimize.Create(m_chart_id, "MinimizeBtn", m_subwin, PANEL_WIDTH - 60, 5, PANEL_WIDTH - 40, 20);
    m_btnMinimize.Text("−");
    m_btnMinimize.Color(COLOR_TEXT_TITLE);
    m_btnMinimize.ColorBackground(COLOR_TITLE_BG);
    m_btnMinimize.ColorBorder(COLOR_TITLE_BG);
    m_btnMinimize.FontSize(12);
    Add(m_btnMinimize);

    // Close butonu
    m_btnClose.Create(m_chart_id, "CloseBtn", m_subwin, PANEL_WIDTH - 35, 5, PANEL_WIDTH - 15, 20);
    m_btnClose.Text("✖");
    m_btnClose.Color(COLOR_TEXT_TITLE);
    m_btnClose.ColorBackground(COLOR_TITLE_BG);
    m_btnClose.ColorBorder(COLOR_TITLE_BG);
    m_btnClose.FontSize(10);
    Add(m_btnClose);
}

//+------------------------------------------------------------------+
//| Sol sütun oluştur - Mükemmel hizalama                          |
//+------------------------------------------------------------------+
void CorporateTradeDialog::CreateLeftColumn()
{
    int leftX = MARGIN;
    int currentY = 10;
    
    // TEKNİK İNDİKATÖRLER başlığı
    m_lblIndicatorTitle.Create(m_chart_id, "IndicatorTitle", m_subwin, leftX, currentY, leftX + COLUMN_WIDTH, currentY + 20);
    m_lblIndicatorTitle.Text("📈 TEKNİK İNDİKATÖRLER");
    m_lblIndicatorTitle.Color(COLOR_TEXT_NORMAL);
    m_lblIndicatorTitle.FontSize(9);
    Add(m_lblIndicatorTitle);
    currentY += 25;
    
    // İndikatör bölümü
    int indicatorY = currentY;
    
    // RSI
    m_lblRSI.Create(m_chart_id, "RSI", m_subwin, leftX + 10, currentY, leftX + 60, currentY + 18);
    m_lblRSI.Text("RSI:");
    m_lblRSI.Color(COLOR_TEXT_NORMAL);
    m_lblRSI.FontSize(8);
    Add(m_lblRSI);
    
    m_lblRSIValue.Create(m_chart_id, "RSIValue", m_subwin, leftX + 70, currentY, leftX + 150, currentY + 18);
    m_lblRSIValue.Text("0.0");
    m_lblRSIValue.Color(COLOR_TEXT_NORMAL);
    m_lblRSIValue.FontSize(8);
    Add(m_lblRSIValue);
    currentY += 22;
    
    // MA
    m_lblMA.Create(m_chart_id, "MA", m_subwin, leftX + 10, currentY, leftX + 60, currentY + 18);
    m_lblMA.Text("MA(20):");
    m_lblMA.Color(COLOR_TEXT_NORMAL);
    m_lblMA.FontSize(8);
    Add(m_lblMA);
    
    m_lblMAValue.Create(m_chart_id, "MAValue", m_subwin, leftX + 70, currentY, leftX + 150, currentY + 18);
    m_lblMAValue.Text("0.0000");
    m_lblMAValue.Color(COLOR_TEXT_NORMAL);
    m_lblMAValue.FontSize(8);
    Add(m_lblMAValue);
    currentY += 22;
    
    // ATR
    m_lblATR.Create(m_chart_id, "ATR", m_subwin, leftX + 10, currentY, leftX + 60, currentY + 18);
    m_lblATR.Text("ATR:");
    m_lblATR.Color(COLOR_TEXT_NORMAL);
    m_lblATR.FontSize(8);
    Add(m_lblATR);
    
    m_lblATRValue.Create(m_chart_id, "ATRValue", m_subwin, leftX + 70, currentY, leftX + 150, currentY + 18);
    m_lblATRValue.Text("0.0000");
    m_lblATRValue.Color(COLOR_TEXT_NORMAL);
    m_lblATRValue.FontSize(8);
    Add(m_lblATRValue);
    currentY += 35;
    
    // POZİSYON BİLGİLERİ başlığı
    m_lblPositionTitle.Create(m_chart_id, "PositionTitle", m_subwin, leftX, currentY, leftX + COLUMN_WIDTH, currentY + 20);
    m_lblPositionTitle.Text("💼 POZİSYON BİLGİLERİ");
    m_lblPositionTitle.Color(COLOR_TEXT_NORMAL);
    m_lblPositionTitle.FontSize(9);
    Add(m_lblPositionTitle);
    currentY += 25;
    
    // Pozisyon bilgileri
    m_lblPosCount.Create(m_chart_id, "PosCount", m_subwin, leftX + 10, currentY, leftX + COLUMN_WIDTH - 10, currentY + 18);
    m_lblPosCount.Text("Açık Pozisyon: 0");
    m_lblPosCount.Color(COLOR_TEXT_NORMAL);
    m_lblPosCount.FontSize(8);
    Add(m_lblPosCount);
    currentY += 20;
    
    m_lblTotalPL.Create(m_chart_id, "TotalPL", m_subwin, leftX + 10, currentY, leftX + COLUMN_WIDTH - 10, currentY + 18);
    m_lblTotalPL.Text("Toplam P&L: $0.00");
    m_lblTotalPL.Color(COLOR_TEXT_NORMAL);
    m_lblTotalPL.FontSize(8);
    Add(m_lblTotalPL);
    currentY += 20;
    
    m_lblDailyPL.Create(m_chart_id, "DailyPL", m_subwin, leftX + 10, currentY, leftX + COLUMN_WIDTH - 10, currentY + 18);
    m_lblDailyPL.Text("Günlük P&L: $0.00");
    m_lblDailyPL.Color(COLOR_TEXT_NORMAL);
    m_lblDailyPL.FontSize(8);
    Add(m_lblDailyPL);
    currentY += 20;
    
    m_lblTotalVol.Create(m_chart_id, "TotalVol", m_subwin, leftX + 10, currentY, leftX + COLUMN_WIDTH - 10, currentY + 18);
    m_lblTotalVol.Text("Toplam Hacim: 0.00");
    m_lblTotalVol.Color(COLOR_TEXT_NORMAL);
    m_lblTotalVol.FontSize(8);
    Add(m_lblTotalVol);
    currentY += 20;
    
    m_lblProfitCount.Create(m_chart_id, "ProfitCount", m_subwin, leftX + 10, currentY, leftX + COLUMN_WIDTH - 10, currentY + 18);
    m_lblProfitCount.Text("Karlı: 0 | Zararlı: 0");
    m_lblProfitCount.Color(COLOR_TEXT_NORMAL);
    m_lblProfitCount.FontSize(8);
    Add(m_lblProfitCount);
}

//+------------------------------------------------------------------+
//| Sağ sütun oluştur - Elle düzenlenebilir inputlar               |
//+------------------------------------------------------------------+
void CorporateTradeDialog::CreateRightColumn()
{
    int rightX = MARGIN + COLUMN_WIDTH + SPACING;
    int currentY = 10;

    // HIZLI İŞLEM başlığı
    m_lblTradeTitle.Create(m_chart_id, "TradeTitle", m_subwin, rightX, currentY, rightX + COLUMN_WIDTH, currentY + 20);
    m_lblTradeTitle.Text("⚡ HIZLI İŞLEM");
    m_lblTradeTitle.Color(COLOR_TEXT_NORMAL);
    m_lblTradeTitle.FontSize(9);
    Add(m_lblTradeTitle);
    currentY += 30;

    // LOT ayarı
    m_lblLot.Create(m_chart_id, "LotLabel", m_subwin, rightX, currentY, rightX + 60, currentY + 18);
    m_lblLot.Text("LOT:");
    m_lblLot.Color(COLOR_TEXT_NORMAL);
    m_lblLot.FontSize(8);
    Add(m_lblLot);

    m_editLot.Create(m_chart_id, "LotEdit", m_subwin, rightX + 70, currentY - 2, rightX + 70 + INPUT_WIDTH, currentY + 18);
    m_editLot.Text(DoubleToString(DefaultLot, 2));
    m_editLot.ColorBackground(COLOR_INPUT_BG);
    m_editLot.ColorBorder(COLOR_BORDER);
    m_editLot.FontSize(8);
    Add(m_editLot);
    currentY += 30;

    // SL ayarı
    m_lblSL.Create(m_chart_id, "SLLabel", m_subwin, rightX, currentY, rightX + 60, currentY + 18);
    m_lblSL.Text("SL (pip):");
    m_lblSL.Color(COLOR_TEXT_NORMAL);
    m_lblSL.FontSize(8);
    Add(m_lblSL);

    m_editSL.Create(m_chart_id, "SLEdit", m_subwin, rightX + 70, currentY - 2, rightX + 70 + INPUT_WIDTH, currentY + 18);
    m_editSL.Text(IntegerToString(DefaultSL));
    m_editSL.ColorBackground(COLOR_INPUT_BG);
    m_editSL.ColorBorder(COLOR_BORDER);
    m_editSL.FontSize(8);
    Add(m_editSL);
    currentY += 30;

    // TP ayarı
    m_lblTP.Create(m_chart_id, "TPLabel", m_subwin, rightX, currentY, rightX + 60, currentY + 18);
    m_lblTP.Text("TP (pip):");
    m_lblTP.Color(COLOR_TEXT_NORMAL);
    m_lblTP.FontSize(8);
    Add(m_lblTP);

    m_editTP.Create(m_chart_id, "TPEdit", m_subwin, rightX + 70, currentY - 2, rightX + 70 + INPUT_WIDTH, currentY + 18);
    m_editTP.Text(IntegerToString(DefaultTP));
    m_editTP.ColorBackground(COLOR_INPUT_BG);
    m_editTP.ColorBorder(COLOR_BORDER);
    m_editTP.FontSize(8);
    Add(m_editTP);
    currentY += 40;

    // BUY/SELL butonları - Güzelleştirilmiş
    m_btnBuy.Create(m_chart_id, "BuyBtn", m_subwin, rightX, currentY, rightX + BUTTON_WIDTH, currentY + BUTTON_HEIGHT);
    m_btnBuy.Text("🟢 BUY");
    m_btnBuy.Color(clrWhite);
    m_btnBuy.ColorBackground(COLOR_BUTTON_BUY);
    m_btnBuy.ColorBorder(COLOR_BUTTON_BUY);
    m_btnBuy.FontSize(10);
    Add(m_btnBuy);

    m_btnSell.Create(m_chart_id, "SellBtn", m_subwin, rightX + BUTTON_WIDTH + 10, currentY, rightX + 2*BUTTON_WIDTH + 10, currentY + BUTTON_HEIGHT);
    m_btnSell.Text("🔴 SELL");
    m_btnSell.Color(clrWhite);
    m_btnSell.ColorBackground(COLOR_BUTTON_SELL);
    m_btnSell.ColorBorder(COLOR_BUTTON_SELL);
    m_btnSell.FontSize(10);
    Add(m_btnSell);
    currentY += 50;

    // POZİSYON YÖNETİMİ başlığı
    m_lblManageTitle.Create(m_chart_id, "ManageTitle", m_subwin, rightX, currentY, rightX + COLUMN_WIDTH, currentY + 20);
    m_lblManageTitle.Text("🎯 POZİSYON YÖNETİMİ");
    m_lblManageTitle.Color(COLOR_TEXT_NORMAL);
    m_lblManageTitle.FontSize(9);
    Add(m_lblManageTitle);
    currentY += 30;

    // Yönetim butonları - Güzelleştirilmiş
    m_btnCloseProfit.Create(m_chart_id, "CloseProfitBtn", m_subwin, rightX, currentY, rightX + COLUMN_WIDTH - 10, currentY + 25);
    m_btnCloseProfit.Text("💚 Karlı Pozisyonları Kapat");
    m_btnCloseProfit.Color(clrWhite);
    m_btnCloseProfit.ColorBackground(COLOR_BUTTON_MANAGE);
    m_btnCloseProfit.ColorBorder(COLOR_BUTTON_MANAGE);
    m_btnCloseProfit.FontSize(9);
    Add(m_btnCloseProfit);
    currentY += 35;

    m_btnCloseLoss.Create(m_chart_id, "CloseLossBtn", m_subwin, rightX, currentY, rightX + COLUMN_WIDTH - 10, currentY + 25);
    m_btnCloseLoss.Text("❤️ Zararlı Pozisyonları Kapat");
    m_btnCloseLoss.Color(clrWhite);
    m_btnCloseLoss.ColorBackground(COLOR_BUTTON_MANAGE);
    m_btnCloseLoss.ColorBorder(COLOR_BUTTON_MANAGE);
    m_btnCloseLoss.FontSize(9);
    Add(m_btnCloseLoss);
    currentY += 35;

    m_btnCloseAll.Create(m_chart_id, "CloseAllBtn", m_subwin, rightX, currentY, rightX + COLUMN_WIDTH - 10, currentY + 25);
    m_btnCloseAll.Text("🚫 Tüm Pozisyonları Kapat");
    m_btnCloseAll.Color(clrWhite);
    m_btnCloseAll.ColorBackground(COLOR_BUTTON_MANAGE);
    m_btnCloseAll.ColorBorder(COLOR_BUTTON_MANAGE);
    m_btnCloseAll.FontSize(9);
    Add(m_btnCloseAll);
}

//+------------------------------------------------------------------+
//| İndikatörleri güncelle                                          |
//+------------------------------------------------------------------+
void CorporateTradeDialog::UpdateIndicators()
{
    // RSI hesapla
    int rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
    if(rsiHandle != INVALID_HANDLE)
    {
        double rsiBuffer[1];
        if(CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer) > 0)
        {
            g_rsiValue = rsiBuffer[0];
        }
        IndicatorRelease(rsiHandle);
    }

    // MA hesapla
    int maHandle = iMA(_Symbol, PERIOD_CURRENT, MA_Period, 0, MODE_SMA, PRICE_CLOSE);
    if(maHandle != INVALID_HANDLE)
    {
        double maBuffer[1];
        if(CopyBuffer(maHandle, 0, 0, 1, maBuffer) > 0)
        {
            g_maValue = maBuffer[0];
        }
        IndicatorRelease(maHandle);
    }

    // ATR hesapla
    int atrHandle = iATR(_Symbol, PERIOD_CURRENT, 14);
    if(atrHandle != INVALID_HANDLE)
    {
        double atrBuffer[1];
        if(CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) > 0)
        {
            g_atrValue = atrBuffer[0];
        }
        IndicatorRelease(atrHandle);
    }

    // Görünümü güncelle
    UpdateIndicatorDisplay();
}

//+------------------------------------------------------------------+
//| İndikatör görünümünü güncelle                                   |
//+------------------------------------------------------------------+
void CorporateTradeDialog::UpdateIndicatorDisplay()
{
    // RSI güncelle ve renk değiştir
    color rsiColor = COLOR_TEXT_NORMAL;
    if(g_rsiValue <= RSI_Oversold)
        rsiColor = clrLimeGreen;
    else if(g_rsiValue >= RSI_Overbought)
        rsiColor = clrTomato;

    m_lblRSIValue.Text(DoubleToString(g_rsiValue, 1));
    m_lblRSIValue.Color(rsiColor);

    // MA güncelle
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    color maColor = COLOR_TEXT_NORMAL;
    if(currentPrice > g_maValue)
        maColor = clrLimeGreen;
    else if(currentPrice < g_maValue)
        maColor = clrTomato;

    m_lblMAValue.Text(DoubleToString(g_maValue, _Digits));
    m_lblMAValue.Color(maColor);

    // ATR güncelle
    color atrColor = COLOR_TEXT_NORMAL;
    if(g_atrValue >= ATR_High)
        atrColor = clrOrange;

    m_lblATRValue.Text(DoubleToString(g_atrValue, _Digits + 1));
    m_lblATRValue.Color(atrColor);
}

//+------------------------------------------------------------------+
//| İndikatör görünümünü güncelle                                   |
//+------------------------------------------------------------------+
void CorporateTradeDialog::UpdateIndicatorDisplay()
{
    // RSI güncelle ve renk değiştir
    color rsiColor = COLOR_TEXT_NORMAL;
    if(g_rsiValue <= RSI_Oversold)
        rsiColor = clrLimeGreen;
    else if(g_rsiValue >= RSI_Overbought)
        rsiColor = clrTomato;

    m_lblRSIValue.Text(DoubleToString(g_rsiValue, 1));
    m_lblRSIValue.Color(rsiColor);

    // MA güncelle
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    color maColor = COLOR_TEXT_NORMAL;
    if(currentPrice > g_maValue)
        maColor = clrLimeGreen;
    else if(currentPrice < g_maValue)
        maColor = clrTomato;

    m_lblMAValue.Text(DoubleToString(g_maValue, _Digits));
    m_lblMAValue.Color(maColor);

    // ATR güncelle
    color atrColor = COLOR_TEXT_NORMAL;
    if(g_atrValue >= ATR_High)
        atrColor = clrOrange;

    m_lblATRValue.Text(DoubleToString(g_atrValue, _Digits + 1));
    m_lblATRValue.Color(atrColor);
}

//+------------------------------------------------------------------+
//| Pozisyon verilerini güncelle                                    |
//+------------------------------------------------------------------+
void CorporateTradeDialog::UpdatePositionData()
{
    int positionCount = 0;
    double totalVolume = 0;
    double totalProfit = 0;
    int profitableCount = 0;
    int losingCount = 0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);

        positionCount++;
        totalVolume += volume;
        totalProfit += profit;

        if(profit > 0)
            profitableCount++;
        else if(profit < 0)
            losingCount++;
    }

    double dailyPL = CalculateDailyPL();

    // UI güncelle
    m_lblPosCount.Text("Açık Pozisyon: " + IntegerToString(positionCount));

    color plColor = (totalProfit >= 0) ? clrLimeGreen : clrTomato;
    m_lblTotalPL.Text("Toplam P&L: $" + DoubleToString(totalProfit, 2));
    m_lblTotalPL.Color(plColor);

    color dailyColor = (dailyPL >= 0) ? clrLimeGreen : clrTomato;
    m_lblDailyPL.Text("Günlük P&L: $" + DoubleToString(dailyPL, 2));
    m_lblDailyPL.Color(dailyColor);

    m_lblTotalVol.Text("Toplam Hacim: " + DoubleToString(totalVolume, 2));
    m_lblProfitCount.Text("Karlı: " + IntegerToString(profitableCount) + " | Zararlı: " + IntegerToString(losingCount));
}

//+------------------------------------------------------------------+
//| Günlük P&L hesapla                                             |
//+------------------------------------------------------------------+
double CorporateTradeDialog::CalculateDailyPL()
{
    double dailyPL = 0;
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    HistorySelect(todayStart, TimeCurrent());

    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
            if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);

                dailyPL += (profit + commission + swap);
            }
        }
    }

    return dailyPL;
}

//+------------------------------------------------------------------+
//| Tick olayı                                                      |
//+------------------------------------------------------------------+
void CorporateTradeDialog::OnTick()
{
    if(TimeCurrent() - m_lastUpdate >= 2)
    {
        UpdateIndicators();
        UpdatePositionData();
        m_lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Olay işleyici                                                   |
//+------------------------------------------------------------------+
bool CorporateTradeDialog::OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == "BuyBtn")
        {
            ExecuteBuyOrder();
            return true;
        }
        else if(sparam == "SellBtn")
        {
            ExecuteSellOrder();
            return true;
        }
        else if(sparam == "CloseProfitBtn")
        {
            CloseProfitablePositions();
            return true;
        }
        else if(sparam == "CloseLossBtn")
        {
            CloseLosingPositions();
            return true;
        }
        else if(sparam == "CloseAllBtn")
        {
            CloseAllPositions();
            return true;
        }
        else if(sparam == "MinimizeBtn")
        {
            ToggleMinimize();
            return true;
        }
        else if(sparam == "CloseBtn")
        {
            Destroy(0);
            return true;
        }
    }

    return CDialog::OnEvent(id, lparam, dparam, sparam);
}

//+------------------------------------------------------------------+
//| Buy emri ver                                                     |
//+------------------------------------------------------------------+
void CorporateTradeDialog::ExecuteBuyOrder()
{
    CTrade trade;

    double lot = StringToDouble(m_editLot.Text());
    int sl = (int)StringToInteger(m_editSL.Text());
    int tp = (int)StringToInteger(m_editTP.Text());

    double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double slPrice = price - (sl * _Point * 10);
    double tpPrice = price + (tp * _Point * 10);

    if(trade.Buy(lot, _Symbol, price, slPrice, tpPrice, "Corporate Panel Buy"))
    {
        Print("✅ BUY emri başarılı - Lot: ", lot, " SL: ", sl, " TP: ", tp);
    }
    else
    {
        Print("❌ BUY emri başarısız - Hata: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Sell emri ver                                                    |
//+------------------------------------------------------------------+
void CorporateTradeDialog::ExecuteSellOrder()
{
    CTrade trade;

    double lot = StringToDouble(m_editLot.Text());
    int sl = (int)StringToInteger(m_editSL.Text());
    int tp = (int)StringToInteger(m_editTP.Text());

    double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double slPrice = price + (sl * _Point * 10);
    double tpPrice = price - (tp * _Point * 10);

    if(trade.Sell(lot, _Symbol, price, slPrice, tpPrice, "Corporate Panel Sell"))
    {
        Print("✅ SELL emri başarılı - Lot: ", lot, " SL: ", sl, " TP: ", tp);
    }
    else
    {
        Print("❌ SELL emri başarısız - Hata: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Pozisyon kapatma fonksiyonları                                  |
//+------------------------------------------------------------------+
void CorporateTradeDialog::CloseProfitablePositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "" || PositionGetDouble(POSITION_PROFIT) <= 0)
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("💚 ", closedCount, " karlı pozisyon kapatıldı");
}

void CorporateTradeDialog::CloseLosingPositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "" || PositionGetDouble(POSITION_PROFIT) >= 0)
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("❤️ ", closedCount, " zararlı pozisyon kapatıldı");
}

void CorporateTradeDialog::CloseAllPositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("🚫 ", closedCount, " pozisyon kapatıldı");
}

//+------------------------------------------------------------------+
//| Panel minimize/restore                                           |
//+------------------------------------------------------------------+
void CorporateTradeDialog::ToggleMinimize()
{
    if(m_isMinimized)
    {
        // Restore - Paneli büyüt
        Resize(PANEL_WIDTH, PANEL_HEIGHT);
        m_btnMinimize.Text("−");

        // Tüm kontrolleri görünür yap
        ShowAllControls(true);
        m_isMinimized = false;

        Print("📊 Panel restore edildi");
    }
    else
    {
        // Minimize - Paneli küçült
        Resize(PANEL_WIDTH, 30);
        m_btnMinimize.Text("□");

        // Sadece başlık çubuğunu görünür bırak
        ShowAllControls(false);
        m_isMinimized = true;

        Print("📊 Panel minimize edildi");
    }
}

//+------------------------------------------------------------------+
//| Tüm kontrolleri göster/gizle                                    |
//+------------------------------------------------------------------+
void CorporateTradeDialog::ShowAllControls(bool show)
{
    // Sol sütun kontrolleri
    m_lblIndicatorTitle.Show(show);
    m_lblRSI.Show(show);
    m_lblRSIValue.Show(show);
    m_lblMA.Show(show);
    m_lblMAValue.Show(show);
    m_lblATR.Show(show);
    m_lblATRValue.Show(show);
    m_lblPositionTitle.Show(show);
    m_lblPosCount.Show(show);
    m_lblTotalPL.Show(show);
    m_lblDailyPL.Show(show);
    m_lblTotalVol.Show(show);
    m_lblProfitCount.Show(show);

    // Sağ sütun kontrolleri
    m_lblTradeTitle.Show(show);
    m_lblLot.Show(show);
    m_editLot.Show(show);
    m_lblSL.Show(show);
    m_editSL.Show(show);
    m_lblTP.Show(show);
    m_editTP.Show(show);
    m_btnBuy.Show(show);
    m_btnSell.Show(show);
    m_lblManageTitle.Show(show);
    m_btnCloseProfit.Show(show);
    m_btnCloseLoss.Show(show);
    m_btnCloseAll.Show(show);
}

//+------------------------------------------------------------------+
//| Global değişkenler                                              |
//+------------------------------------------------------------------+
CorporateTradeDialog* g_tradeDialog = NULL;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    g_tradeDialog = new CorporateTradeDialog();

    if(!g_tradeDialog.Create(0, "ImprovedCorporatePanel", 0, StartX, StartY))
    {
        Print("❌ Improved Corporate Panel oluşturulamadı!");
        delete g_tradeDialog;
        g_tradeDialog = NULL;
        return INIT_FAILED;
    }

    Print("✅ Improved Corporate Panel başlatıldı - Mükemmel hizalama!");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(g_tradeDialog != NULL)
    {
        g_tradeDialog.Destroy(reason);
        delete g_tradeDialog;
        g_tradeDialog = NULL;
    }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(g_tradeDialog != NULL)
    {
        g_tradeDialog.OnTick();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(g_tradeDialog != NULL)
    {
        g_tradeDialog.OnEvent(id, lparam, dparam, sparam);
    }
}
