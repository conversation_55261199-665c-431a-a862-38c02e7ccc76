# 🛡️ SENTINEL Trading Sistemi

## 📋 İçindekiler
- [<PERSON><PERSON>](#genel-bakış)
- [AutoTrade Karar Matrisi](#autotrade-karar-matrisi)
- [Formasyon Sistemi](#formasyon-sistemi)
- [<PERSON><PERSON><PERSON> ve Kullanım](#kurulum-ve-kullanım)
- [Parametreler](#parametreler)
- [Risk Yönetimi](#risk-yönetimi)
- [Telegram Entegrasyonu](#telegram-entegrasyonu)

## 🎯 Genel Bakış

SENTINEL, MetaTrader 5 için geliştirilmiş gelişmiş bir trading sistemidir. Teknik analiz formasyonlarını otomatik tespit eder, gü<PERSON>li giriş noktalarını belirler ve risk yönetimi ile otomatik işlem gerçekleştirir.

### ✨ Ana Özellikler
- 🔍 **Formasyon Tespiti:** Double Top/Bottom, Wedge, Engu<PERSON><PERSON>, <PERSON>, Marubozu
- 🛡️ **Güvenlik Sistemi:** Support/Resistance tabanlı güvenli alan <PERSON>
- 🤖 **AutoTrade:** Tam otomatik işlem sistemi
- 📊 **Trend Filtresi:** Hareketli ortalama tabanlı trend analizi
- 📱 **Telegram Bildirimleri:** Gerçek zamanlı sinyal bildirimleri
- 🎨 **Görsel Panel:** Canlı trading bilgileri

## 🧠 AutoTrade Karar Matrisi

### 📊 Karar Akış Şeması

```
📈 SİNYAL TESPİT EDİLDİ
         ↓
🔍 GÜVENLİK KONTROLLERİ
         ↓
┌─────────────────────────┐
│  1. Support/Resistance  │ ← Temel güvenlik
│     Seviye Kontrolü     │
└─────────────────────────┘
         ↓
┌─────────────────────────┐
│  2. Güvenli Alan        │ ← SafeZone kontrolü
│     Temas Kontrolü      │
└─────────────────────────┘
         ↓
┌─────────────────────────┐
│  3. Spread Kontrolü     │ ← Maliyet kontrolü
└─────────────────────────┘
         ↓
┌─────────────────────────┐
│  4. Günlük Limit        │ ← Risk kontrolü
│     Kontrolü            │
└─────────────────────────┘
         ↓
┌─────────────────────────┐
│  5. Drawdown Kontrolü   │ ← Sermaye koruması
└─────────────────────────┘
         ↓
✅ İŞLEM GERÇEKLEŞTİR
```

### 🎯 BUY Sinyali Karar Matrisi

| Kontrol | Kriter | Açıklama |
|---------|--------|----------|
| **1. Formasyon** | ✅ BUY formasyonu tespit | Double Bottom, Falling Wedge, Bullish Engulfing, vb. |
| **2. Support Kontrolü** | `barLow > currentSupportLevel` | Bar support seviyesinin üstünde |
| **3. DownSafe Temas** | `barLow ≤ ds ≤ barHigh` | Bar DownSafe seviyesine temas ediyor |
| **4. Spread** | `spread ≤ MaxSpread` | Spread kabul edilebilir seviyede |
| **5. Günlük Limit** | `dailyTrades < MaxDailyTrades` | Günlük işlem limiti aşılmamış |
| **6. Drawdown** | `drawdown < MaxDrawdown` | Maksimum kayıp limiti aşılmamış |

### 🎯 SELL Sinyali Karar Matrisi

| Kontrol | Kriter | Açıklama |
|---------|--------|----------|
| **1. Formasyon** | ✅ SELL formasyonu tespit | Double Top, Rising Wedge, Bearish Engulfing, vb. |
| **2. Resistance Kontrolü** | `barHigh < currentResistanceLevel` | Bar resistance seviyesinin altında |
| **3. UpSafe Temas** | `barLow ≤ rs ≤ barHigh` | Bar UpSafe seviyesine temas ediyor |
| **4. Spread** | `spread ≤ MaxSpread` | Spread kabul edilebilir seviyede |
| **5. Günlük Limit** | `dailyTrades < MaxDailyTrades` | Günlük işlem limiti aşılmamış |
| **6. Drawdown** | `drawdown < MaxDrawdown` | Maksimum kayıp limiti aşılmamış |

### 🔢 Güvenli Alan Hesaplaması

```cpp
// Güvenli alan formülü
double sfr = (resistance - support) * SafeZoneRatio;
double DownSafe = support + sfr;     // BUY için güvenli seviye
double UpSafe = resistance - sfr;    // SELL için güvenli seviye
```

**Örnek Hesaplama:**
- Support: 1.2000
- Resistance: 1.2100  
- SafeZoneRatio: 0.1 (10%)
- sfr = (1.2100 - 1.2000) * 0.1 = 0.0010
- DownSafe = 1.2000 + 0.0010 = 1.2010
- UpSafe = 1.2100 - 0.0010 = 1.2090

## 🎨 Formasyon Sistemi

### 🏆 Formasyon Öncelik Sıralaması

| Sıra | Formasyon | Güç Puanı | Açıklama |
|------|-----------|-----------|----------|
| 1 | Double Top/Bottom | 100-150 | En güçlü reversal sinyali |
| 2 | Rising/Falling Wedge | 80-130 | Güçlü trend değişim sinyali |
| 3 | Engulfing | 60-110 | Orta güçlü momentum sinyali |
| 4 | Hammer/Shooting Star | 40-90 | Zayıf reversal sinyali |
| 5 | Marubozu | 20-70 | En zayıf momentum sinyali |

### 🎯 Formasyon Çakışma Önleme

- **Minimum Mesafe:** FormationDistance parametresi (varsayılan: 10 bar)
- **Güç Karşılaştırması:** Güçlü formasyon zayıfı ezer
- **Otomatik Temizlik:** Eski formasyonlar periyodik olarak silinir

### 🎨 Görsel Kodlama

| Formasyon | Renk | Sembol | Açıklama |
|-----------|------|--------|----------|
| Double Top | 🔴 Kırmızı | ↓ | SELL sinyali |
| Double Bottom | 🔵 Mavi | ↑ | BUY sinyali |
| Rising Wedge | 🟡 Altın | ↓ | SELL sinyali |
| Falling Wedge | 🟢 Yeşil | ↑ | BUY sinyali |
| BUY Sinyali | 🟢 Yeşil | 🟢 BUY | Alış noktası |
| SELL Sinyali | 🔴 Kırmızı | 🔴 SELL | Satış noktası |

## ⚙️ Kurulum ve Kullanım

### 📥 Kurulum Adımları

1. **Dosya Kopyalama:**
   ```
   Sentinel.mq5 → MQL5/Experts/horizon/
   SentinelPanel.mq5 → MQL5/Indicators/horizon/
   ```

2. **Derleme:**
   - MetaEditor'de Sentinel.mq5'i açın
   - F7 ile derleyin
   - Hataları kontrol edin

3. **Grafik Ekleme:**
   - MT5'te istediğiniz sembolü açın
   - Navigator → Expert Advisors → horizon → Sentinel
   - Grafiğe sürükleyin

### 🔧 İlk Kurulum Ayarları

```
=== Temel Ayarlar ===
EnableAutoTrade = false          // İlk test için kapalı
UseTrendFilter = true           // Trend filtresi açık
ShowTrendInfo = true           // Panel göster
EnableTelegram = false         // İlk test için kapalı

=== Formasyon Ayarları ===
MinCandleSize = 60             // 60 pip minimum
SwingLookback = 2              // 2 bar geriye bakış
FormationDistance = 10         // 10 bar minimum mesafe

=== Risk Ayarları ===
RiskPercent = 1.0              // %1 risk
SafeZoneRatio = 0.1           // %10 güvenli alan
MaxDailyTrades = 5            // Günde 5 işlem
MaxDrawdown = 10.0            // %10 maksimum kayıp
```

## 📊 Parametreler

### 🎯 Trend Filtresi Ayarları

| Parametre | Varsayılan | Açıklama |
|-----------|------------|----------|
| `UseTrendFilter` | true | Trend filtresi aktif/pasif |
| `FastMA_Period` | 20 | Hızlı MA periyodu |
| `SlowMA_Period` | 50 | Yavaş MA periyodu |
| `MA_Method` | SMA | MA hesaplama yöntemi |

### 🔍 Formasyon Ayarları

| Parametre | Varsayılan | Açıklama |
|-----------|------------|----------|
| `MinCandleSize` | 60 | Minimum mum boyutu (pip) |
| `SwingLookback` | 2 | Swing noktası arama mesafesi |
| `DoubleTopTolerance` | 0.5 | Double Top toleransı (%) |
| `MinWedgeLength` | 5 | Minimum Wedge uzunluğu (bar) |
| `FormationDistance` | 10 | Formasyonlar arası minimum mesafe |

### 🛡️ Risk Yönetimi Ayarları

| Parametre | Varsayılan | Açıklama |
|-----------|------------|----------|
| `RiskPercent` | 1.0 | Risk yüzdesi (hesap bakiyesinin %) |
| `SafeZoneRatio` | 0.1 | Güvenli alan oranı (%10) |
| `RiskRewardRatio` | 2.0 | Risk/Ödül oranı (1:2) |
| `MaxDailyTrades` | 5 | Günlük maksimum işlem sayısı |
| `MaxDrawdown` | 10.0 | Maksimum drawdown (%) |
| `MaxSpread` | 3.0 | Maksimum spread (pip) |

## 🛡️ Risk Yönetimi

### 💰 Lot Size Hesaplaması

```cpp
// Risk bazlı lot hesaplama
double riskAmount = AccountBalance * (RiskPercent / 100);
double slDistance = MathAbs(entryPrice - stopLoss);
double slPips = slDistance / _Point;
double lotSize = riskAmount / (slPips * SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE));
```

### 🎯 Stop Loss & Take Profit

- **Stop Loss:** Support/Resistance seviyelerine dayalı
- **Take Profit:** Risk/Ödül oranına göre (varsayılan 1:2)
- **Dinamik Güncelleme:** S/R seviyeleri değiştikçe SL/TP güncellenir

### 📈 Trailing Stop

```cpp
// Trailing stop mantığı
if (profit > TrailingDistance) {
    newSL = currentPrice - TrailingDistance;
    if (newSL > currentSL) {
        UpdateStopLoss(newSL);
    }
}
```

## 📱 Telegram Entegrasyonu

### 🔧 Kurulum

1. **Bot Oluşturma:**
   - @BotFather'a mesaj gönderin
   - `/newbot` komutu ile bot oluşturun
   - Bot token'ını alın

2. **Chat ID Alma:**
   - Bot'a mesaj gönderin
   - `https://api.telegram.org/bot<TOKEN>/getUpdates` adresini ziyaret edin
   - Chat ID'yi kopyalayın

3. **Ayarlama:**
   ```
   EnableTelegram = true
   TelegramBotToken = "YOUR_BOT_TOKEN"
   TelegramChatID = "YOUR_CHAT_ID"
   ```

### 📨 Bildirim Türleri

- **Sinyal Bildirimleri:** Yeni formasyon tespiti
- **İşlem Bildirimleri:** AutoTrade işlem açma/kapama
- **Risk Bildirimleri:** Drawdown, limit aşımları
- **Sistem Bildirimleri:** EA başlatma/durdurma

## 🔧 Sorun Giderme

### ❌ Yaygın Sorunlar

1. **Formasyonlar Çizilmiyor:**
   - MinCandleSize değerini düşürün
   - SwingLookback değerini artırın
   - Timeframe'i kontrol edin

2. **AutoTrade Çalışmıyor:**
   - EnableAutoTrade = true olduğunu kontrol edin
   - Support/Resistance seviyelerinin hesaplandığını kontrol edin
   - Spread kontrolünü kontrol edin

3. **Telegram Bildirimleri Gelmiyor:**
   - Bot token ve chat ID'yi kontrol edin
   - İnternet bağlantısını kontrol edin
   - MT5 terminal ayarlarını kontrol edin

### 📊 Log Analizi

```
🔍 Güvenlik Kontrolü:
   Support: 1.20000
   Resistance: 1.20100
   DownSafe: 1.20010
   UpSafe: 1.20090
   Bar Range: 1.20005 - 1.20015

✅ BUY Sinyali Güvenli:
   Support Üstü: ✅ (1.20005 > 1.20000)
   DownSafe Temas: ✅ (1.20010 bar içinde)
```

## 📈 Performans Optimizasyonu

### ⚡ Hız Optimizasyonu

- **Tick Bazlı Analiz:** Sadece aktif bar için
- **Periyodik Temizlik:** Eski objeler otomatik silinir
- **Akıllı Güncelleme:** Sadece değişen veriler güncellenir

### 🎯 Timeframe Önerileri

| Timeframe | MinCandleSize | SwingLookback | FormationDistance |
|-----------|---------------|---------------|-------------------|
| M1 | 20-30 pip | 2 | 8-10 bar |
| M5 | 30-50 pip | 2-3 | 10-12 bar |
| M15 | 50-80 pip | 3 | 12-15 bar |
| H1 | 80-120 pip | 3-4 | 15-20 bar |
| H4 | 120-200 pip | 4-5 | 20-25 bar |

---

## 📞 Destek

**Geliştirici:** SENTINEL Trading Team  
**Versiyon:** 2.0  
**Son Güncelleme:** 2025-01-15  

**Not:** Bu sistem eğitim amaçlıdır. Gerçek hesaplarda kullanmadan önce demo hesapta test edin.

## 🧠 Detaylı AutoTrade Karar Matrisi

### 🔍 Güvenlik Kontrol Algoritması

#### 1. Support/Resistance Seviye Kontrolü
```cpp
// Dinamik S/R hesaplama
currentSupportLevel = CalculateDynamicSupport();
currentResistanceLevel = CalculateDynamicResistance();

// Seviye geçerliliği kontrolü
if (currentSupportLevel <= 0 || currentResistanceLevel <= 0) {
    return SIGNAL_REJECTED; // Seviyeler hesaplanmamış
}
```

#### 2. Güvenli Alan (SafeZone) Algoritması
```cpp
// SafeZone hesaplama
double range = currentResistanceLevel - currentSupportLevel;
double safeZoneBuffer = range * SafeZoneRatio; // Varsayılan %10

// Güvenli seviyeler
double DownSafe = currentSupportLevel + safeZoneBuffer;  // BUY için
double UpSafe = currentResistanceLevel - safeZoneBuffer; // SELL için

// Temas kontrolü
bool touchesDownSafe = (barLow <= DownSafe && barHigh >= DownSafe);
bool touchesUpSafe = (barLow <= UpSafe && barHigh >= UpSafe);
```

#### 3. BUY Sinyali Karar Ağacı
```
📈 BUY SİNYALİ TESPİT EDİLDİ
         ↓
❓ Bar Support Üstünde mi?
   ├─ HAYIR → ❌ SİNYAL REDDEDİLDİ
   └─ EVET ↓
❓ Bar DownSafe'e Temas Ediyor mu?
   ├─ HAYIR → ❌ SİNYAL REDDEDİLDİ
   └─ EVET ↓
❓ Spread Kabul Edilebilir mi?
   ├─ HAYIR → ❌ SİNYAL REDDEDİLDİ
   └─ EVET ↓
❓ Günlük Limit Aşıldı mı?
   ├─ EVET → ❌ SİNYAL REDDEDİLDİ
   └─ HAYIR ↓
❓ Drawdown Limiti Aşıldı mı?
   ├─ EVET → ❌ SİNYAL REDDEDİLDİ
   └─ HAYIR ↓
✅ BUY İŞLEMİ GERÇEKLEŞTİR
```

#### 4. SELL Sinyali Karar Ağacı
```
📉 SELL SİNYALİ TESPİT EDİLDİ
         ↓
❓ Bar Resistance Altında mı?
   ├─ HAYIR → ❌ SİNYAL REDDEDİLDİ
   └─ EVET ↓
❓ Bar UpSafe'e Temas Ediyor mu?
   ├─ HAYIR → ❌ SİNYAL REDDEDİLDİ
   └─ EVET ↓
❓ Spread Kabul Edilebilir mi?
   ├─ HAYIR → ❌ SİNYAL REDDEDİLDİ
   └─ EVET ↓
❓ Günlük Limit Aşıldı mı?
   ├─ EVET → ❌ SİNYAL REDDEDİLDİ
   └─ HAYIR ↓
❓ Drawdown Limiti Aşıldı mı?
   ├─ EVET → ❌ SİNYAL REDDEDİLDİ
   └─ HAYIR ↓
✅ SELL İŞLEMİ GERÇEKLEŞTİR
```

### 📊 Karar Matrisi Tablosu

| Kontrol Adımı | BUY Koşulu | SELL Koşulu | Başarısızlık Sonucu |
|---------------|------------|-------------|---------------------|
| **Formasyon** | Bullish pattern tespit | Bearish pattern tespit | Sinyal oluşmaz |
| **S/R Seviye** | `barLow > support` | `barHigh < resistance` | ❌ Sinyal reddedilir |
| **SafeZone** | `bar touches DownSafe` | `bar touches UpSafe` | ❌ Sinyal reddedilir |
| **Spread** | `spread ≤ MaxSpread` | `spread ≤ MaxSpread` | ❌ Sinyal reddedilir |
| **Günlük Limit** | `trades < MaxDaily` | `trades < MaxDaily` | ❌ Sinyal reddedilir |
| **Drawdown** | `dd < MaxDrawdown` | `dd < MaxDrawdown` | ❌ AutoTrade durdurulur |

### 🎯 Sinyal Güvenlik Skorlaması

#### Güvenlik Skoru Hesaplama
```cpp
int securityScore = 0;

// Support/Resistance kontrolü (40 puan)
if (barInSafeZone) securityScore += 40;

// Spread kontrolü (20 puan)
if (spreadAcceptable) securityScore += 20;

// Trend uyumu (20 puan)
if (signalMatchesTrend) securityScore += 20;

// Formasyon gücü (20 puan)
securityScore += (formationStrength / 5);

// Minimum güvenlik skoru: 60/100
if (securityScore >= 60) {
    return SIGNAL_APPROVED;
} else {
    return SIGNAL_REJECTED;
}
```

#### Güvenlik Seviyeleri
| Skor | Seviye | Açıklama | Eylem |
|------|--------|----------|-------|
| 90-100 | 🟢 Çok Güvenli | Mükemmel koşullar | ✅ İşlem gerçekleştir |
| 70-89 | 🟡 Güvenli | İyi koşullar | ✅ İşlem gerçekleştir |
| 60-69 | 🟠 Orta | Kabul edilebilir | ✅ Dikkatli işlem |
| 40-59 | 🔴 Riskli | Zayıf koşullar | ❌ İşlem reddet |
| 0-39 | ⚫ Çok Riskli | Tehlikeli koşullar | ❌ İşlem reddet |

### 🔄 Dinamik Risk Yönetimi

#### Adaptif Lot Size
```cpp
// Piyasa volatilitesine göre lot ayarlama
double volatility = CalculateVolatility(14); // 14 bar ATR
double baseRisk = RiskPercent;

if (volatility > averageVolatility * 1.5) {
    // Yüksek volatilite - riski azalt
    adjustedRisk = baseRisk * 0.7;
} else if (volatility < averageVolatility * 0.7) {
    // Düşük volatilite - riski artır
    adjustedRisk = baseRisk * 1.2;
} else {
    adjustedRisk = baseRisk;
}
```

#### Dinamik Stop Loss
```cpp
// Support/Resistance tabanlı dinamik SL
if (positionType == BUY) {
    dynamicSL = currentSupportLevel;

    // Minimum SL mesafesi kontrolü
    double minSLDistance = MinSLDistance * _Point;
    if (entryPrice - dynamicSL < minSLDistance) {
        dynamicSL = entryPrice - minSLDistance;
    }
} else {
    dynamicSL = currentResistanceLevel;

    // Minimum SL mesafesi kontrolü
    double minSLDistance = MinSLDistance * _Point;
    if (dynamicSL - entryPrice < minSLDistance) {
        dynamicSL = entryPrice + minSLDistance;
    }
}
```

### 📈 Performans Metrikleri

#### Sistem Başarı Oranları
| Metrik | Hedef | Açıklama |
|--------|-------|----------|
| **Win Rate** | >60% | Kazanan işlem oranı |
| **Risk/Reward** | 1:2+ | Ortalama kazanç/kayıp oranı |
| **Max Drawdown** | <10% | Maksimum sermaye kaybı |
| **Profit Factor** | >1.5 | Toplam kazanç/toplam kayıp |
| **Sharpe Ratio** | >1.0 | Risk ayarlı getiri |

#### Gerçek Zamanlı İzleme
```cpp
// Performans hesaplama
double totalProfit = 0;
double totalLoss = 0;
int winCount = 0;
int lossCount = 0;

// Her kapatılan işlem için
if (closedProfit > 0) {
    totalProfit += closedProfit;
    winCount++;
} else {
    totalLoss += MathAbs(closedProfit);
    lossCount++;
}

// Metrikleri hesapla
double winRate = (winCount * 100.0) / (winCount + lossCount);
double profitFactor = totalProfit / totalLoss;
double avgWin = totalProfit / winCount;
double avgLoss = totalLoss / lossCount;
double riskReward = avgWin / avgLoss;
```

## 🚨 Risk Uyarı Sistemi

### ⚠️ Otomatik Durdurma Koşulları

1. **Maksimum Drawdown Aşımı**
   ```cpp
   if (currentDrawdown > MaxDrawdown) {
       autoTradeEnabled = false;
       SendAlert("AutoTrade durduruldu - Drawdown limiti aşıldı");
   }
   ```

2. **Günlük Kayıp Limiti**
   ```cpp
   if (dailyLoss > DailyLossLimit) {
       autoTradeEnabled = false;
       SendAlert("AutoTrade durduruldu - Günlük kayıp limiti aşıldı");
   }
   ```

3. **Ardışık Kayıp Limiti**
   ```cpp
   if (consecutiveLosses >= MaxConsecutiveLosses) {
       autoTradeEnabled = false;
       SendAlert("AutoTrade durduruldu - Ardışık kayıp limiti aşıldı");
   }
   ```

### 🔔 Uyarı Seviyeleri

| Seviye | Koşul | Eylem |
|--------|-------|-------|
| 🟢 Normal | Drawdown < 5% | Normal işlem |
| 🟡 Dikkat | Drawdown 5-7% | Risk azaltma |
| 🟠 Uyarı | Drawdown 7-10% | Lot size %50 azalt |
| 🔴 Tehlike | Drawdown > 10% | AutoTrade durdur |

---

## 📚 Ek Kaynaklar

### 🔗 Faydalı Linkler
- [MQL5 Dokümantasyonu](https://www.mql5.com/en/docs)
- [MetaTrader 5 Kullanım Kılavuzu](https://www.metatrader5.com/en/help)
- [Telegram Bot API](https://core.telegram.org/bots/api)

### 📖 Önerilen Okumalar
- Technical Analysis of Financial Markets - John Murphy
- Trading in the Zone - Mark Douglas
- Algorithmic Trading - Ernie Chan

### 🎓 Eğitim Materyalleri
- Formasyon analizi teknikleri
- Risk yönetimi stratejileri
- Algoritmic trading temelleri
