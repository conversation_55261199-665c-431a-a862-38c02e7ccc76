//+------------------------------------------------------------------+
//|                                                     Sentinel.mq5 |
//|                                  Copyright 2024, Horizon Systems |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "2.0"
#property description "Sentinel - Gelişmiş Trading Sinyal ve Analiz Sistemi"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| Forward Declarations                                             |
//+------------------------------------------------------------------+
string GetTimeframeName(ENUM_TIMEFRAMES period);
void CheckPanelMessages();
void ManageActiveTrades();
void CheckAndUpdateSLTP(ulong ticket, ENUM_POSITION_TYPE posType, double currentPrice, double stopLoss, double takeProfit);
bool IsSignalSafe(string signalType, int barIndex);
bool IsSpreadAcceptable();
void ExecuteAutoTrade(string signalType, string pattern, double signalPrice);
void ProcessTelegramUpdates(string response);
void CheckNewBarForAutoTrade();

//+------------------------------------------------------------------+
//| SentinelPanel.mq5 Entegrasyonu                                  |
//+------------------------------------------------------------------+

// Panel artık SentinelPanel.mq5 indicator'ı ile yönetiliyor
int sentinelPanelHandle = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| Eski Panel Objelerini Temizle                                   |
//+------------------------------------------------------------------+
void CleanOldPanelObjects()
{
    // Eski manuel panel objelerini temizle
    ObjectDelete(0, "SENTINEL_Panel");
    ObjectDelete(0, "SENTINEL_Title");

    // Panel elementleri
    for(int i = 0; i < 15; i++)
    {
        ObjectDelete(0, "SENTINEL_Label_" + IntegerToString(i));
        ObjectDelete(0, "SENTINEL_Value_" + IntegerToString(i));
    }

    // Butonlar
    ObjectDelete(0, "SENTINEL_BtnCloseProfit");
    ObjectDelete(0, "SENTINEL_BtnCloseProfit_Text");
    ObjectDelete(0, "SENTINEL_BtnCloseAll");
    ObjectDelete(0, "SENTINEL_BtnCloseAll_Text");

    // Diğer olası panel objeleri
    ObjectDelete(0, "SentinelPopup");

    ChartRedraw(0);
    //Print("🧹 Eski panel objeleri temizlendi");
}

//+------------------------------------------------------------------+
//| SentinelPanel Indicator'ını Başlat                              |
//+------------------------------------------------------------------+
void CreateSentinelPanel()
{
    // Önce eski objeleri temizle
    CleanOldPanelObjects();

    // SentinelPanel indicator'ını yükle
    sentinelPanelHandle = iCustom(_Symbol, _Period, "SentinelPanel");

    if(sentinelPanelHandle == INVALID_HANDLE)
    {
        Print("❌ SentinelPanel indicator yüklenemedi!");
        return;
    }

    Print("✅ SentinelPanel indicator başarıyla yüklendi");
}

//+------------------------------------------------------------------+
//| SentinelPanel'i Kapat                                           |
//+------------------------------------------------------------------+
void CloseSentinelPanel()
{
    if(sentinelPanelHandle != INVALID_HANDLE)
    {
        IndicatorRelease(sentinelPanelHandle);
        sentinelPanelHandle = INVALID_HANDLE;
        Print("✅ SentinelPanel indicator kapatıldı");
    }
}

// Panel butonları artık SentinelPanel.mq5'te yönetiliyor

// Panel silme işlemi artık SentinelPanel.mq5'te yönetiliyor

// Panel pozisyon güncellemesi artık SentinelPanel.mq5'te yönetiliyor

//+------------------------------------------------------------------+
//| Panel bilgilerini SentinelPanel'e gönder                        |
//+------------------------------------------------------------------+
void UpdatePanelInfo(string trend, string lastSignal, double balance, double dailyPL,
                    int openTrades, int totalSignals, int approvedSignals, bool autoTrade)
{
    // Bu bilgiler SentinelPanel.mq5'e global değişkenler veya özel mesajlaşma ile aktarılacak
    // Şu an için bu fonksiyon boş bırakılıyor
}

// Panel Z-order yönetimi artık SentinelPanel.mq5'te yapılıyor

//--- Input parametreleri
input group "=== Formasyon Ayarları ==="
input bool ShowEngulfing = true;              // Sarmalama formasyonlarını göster
input bool ShowHammer = true;                 // Çekiç/Düşen Yıldız göster
input bool ShowMarubozu = true;               // Marubozu göster
input double MinCandleSize = 60;               // Minimum mum boyutu (pip)

input group "=== Trend Filtresi ==="
input bool UseTrendFilter = true;             // Trend filtresi kullan (sadece canlı sinyaller için)
input int FastMA_Period = 20;                 // Hızlı Hareketli Ortalama periyodu
input int SlowMA_Period = 50;                 // Yavaş Hareketli Ortalama periyodu
input ENUM_MA_METHOD MA_Method = MODE_SMA;    // Hareketli Ortalama metodu
input ENUM_APPLIED_PRICE MA_Price = PRICE_CLOSE; // Hareketli Ortalama fiyat türü

input group "=== Sinyal Ayarları ==="
input bool EnableCurrentBarSignals = true;   // Aktif barda sinyal ver
input bool EnableTickBasedAnalysis = true;   // Tick bazlı analiz
input bool UseTrendFilterForLiveSignals = true; // Canlı sinyaller için trend filtresi
input int MinTicksForSignal = 5;             // Minimum tick sayısı

input group "=== Destek/Direnç ==="
input bool ShowSupportResistance = true;     // Destek/Direnç çizgilerini göster
input int SR_LookbackPeriod = 100;           // Geriye bakış periyodu
input int SR_MinTouchCount = 2;              // Minimum test sayısı
input double SR_TouchTolerance = 0.5;        // Test toleransı (XAUUSD için pip cinsinden)
input color SupportColor = clrGreen;         // Destek çizgisi rengi
input color ResistanceColor = clrRed;        // Direnç çizgisi rengi
input color DownSafeColor = clrLimeGreen;    // DownSafe çizgi rengi
input color UpSafeColor = clrOrange;         // UpSafe çizgi rengi
input int SR_LineWidth = 2;                  // Çizgi kalınlığı
input ENUM_LINE_STYLE SR_LineStyle = STYLE_SOLID; // Çizgi stili
input ENUM_LINE_STYLE SafeZone_LineStyle = STYLE_DOT; // Safe zone çizgi stili

input group "=== Auto Trade Ayarları ==="
input bool EnableAutoTrade = false;          // Otomatik işlem açık/kapalı
input double SafeZoneRatio = 0.1;            // Güvenli alan oranı (%10)
input double RiskPercent = 1.0;              // Risk yüzdesi (hesap bakiyesinin %)
input int MagicNumber = 12345;               // Magic number (benzersiz tanımlayıcı)
input int MaxDailyTrades = 5;                // Günlük maksimum işlem sayısı
input double MaxDrawdown = 10.0;             // Maksimum drawdown yüzdesi
input double MaxSpread = 3.0;               // Maksimum spread (pip)
input bool ShowSafeZones = true;            // Güvenli alan çizgilerini göster

input group "=== SL/TP Ayarları ==="
enum SL_MODE
{
    SL_SUPPORT_RESISTANCE,    // Support/Resistance seviyelerine göre (toleranslı)
    SL_FIXED_PIPS,           // Sabit pip mesafesi
    SL_ATR_MULTIPLE          // ATR katı
};

enum TP_MODE
{
    TP_RISK_REWARD,          // Risk/Reward oranına göre
    TP_FIXED_PIPS,           // Sabit pip mesafesi
    TP_ATR_MULTIPLE,         // ATR katı
    TP_FIXED_DOLLAR          // Sabit dolar hedefi
};

input SL_MODE StopLossMode = SL_SUPPORT_RESISTANCE;  // Stop Loss hesaplama yöntemi
input double FixedSLPips = 50.0;                     // Sabit SL mesafesi (pip)
input double SL_ATR_Multiplier = 2.0;                // SL için ATR çarpanı
input double SL_TolerancePoints = 50.0;              // Support/Resistance tolerans payı (point)

input TP_MODE TakeProfitMode = TP_FIXED_DOLLAR;      // Take Profit hesaplama yöntemi
input double RiskRewardRatio = 2.0;                  // Risk/Ödül oranı (1:2)
input double FixedTPPips = 100.0;                    // Sabit TP mesafesi (pip)
input double TP_ATR_Multiplier = 3.0;                // TP için ATR çarpanı
input double FixedTPDollar = 2.0;                    // Sabit TP hedefi (dolar)

input group "=== Dinamik SL/TP Ayarları ==="
input bool UseDynamicSLTP = false;              // Dinamik SL/TP sistemi kullan
input double DynamicTPPoints = 1000.0;          // TP mesafesi (point cinsinden)

input group "=== Trailing Stop Ayarları ==="
input bool UseTrailingStop = true;          // Trailing stop kullan
input double TrailingDistance = 20.0;       // Trailing stop mesafesi (pip)

input group "=== Telegram Bildirimleri ==="
input bool EnableTelegram = false;           // Telegram bildirimlerini etkinleştir
input string TelegramBotToken = "";          // Bot Token (BotFather'dan alın)
input string TelegramChatID = "";            // Chat ID (bot ile konuşup /start yazın)
input bool SendSignalAlerts = true;          // Sinyal bildirimlerini gönder
input bool SendSRAlerts = true;              // Destek/Direnç bildirimlerini gönder
input bool SendTrendAlerts = true;           // Trend değişim bildirimlerini gönder
input bool SendTradeAlerts = true;           // İşlem bildirimlerini gönder
input bool EnableTelegramCommands = true;    // Telegram komutlarını etkinleştir
input int CommandCheckInterval = 1;         // Komut kontrol aralığı (saniye)

input group "=== Görsel Ayarlar ==="
input color BuySignalColor = clrLime;         // Alış sinyal rengi
input color SellSignalColor = clrRed;         // Satış sinyal rengi
input int ArrowSize = 2;                      // Ok boyutu
input double BuyArrowDistance = 0.1;          // ALIŞ ok uzaklığı (bar yüksekliğinin %)
input double SellArrowDistance = 0.1;         // SATIŞ ok uzaklığı (bar yüksekliğinin %)
input bool ShowAlerts = true;                 // Uyarı göster
input bool ShowTrendInfo = true;             // Kurumsal panel göster

input group "=== Panel Ayarları ==="
input color PanelBackgroundColor = C'25,25,25';    // Panel arka plan rengi
input color PanelBorderColor = C'70,70,70';        // Panel çerçeve rengi
input color PanelTextColor = clrWhite;             // Panel metin rengi
input color PanelHeaderColor = C'0,120,215';       // Panel başlık rengi
input color ProfitColor = clrLimeGreen;            // Kar rengi
input color LossColor = clrTomato;                 // Zarar rengi

//--- Global değişkenler
datetime lastBarTime = 0;
int fastMA_Handle = INVALID_HANDLE;
int slowMA_Handle = INVALID_HANDLE;
int currentBarTickCount = 0;
datetime lastTickTime = 0;
string lastCurrentBarSignal = "";  // Son aktif bar sinyali

// Sinyal yönü kontrolü için değişkenler
string lastSignalDirection = "";        // Son sinyal yönü (BUY/SELL)
string lastHistoricalDirection = "";    // Son geçmiş analiz sinyal yönü

// Dinamik SL/TP için değişkenler
struct DynamicTradeInfo
{
    ulong ticket;                        // İşlem ticket'ı
    datetime signalBarTime;              // Sinyal barının zamanı
    double signalBarLow;                 // Sinyal barının low'u (BUY için)
    double signalBarHigh;                // Sinyal barının high'u (SELL için)
    string signalType;                   // BUY veya SELL
    bool isActive;                       // İşlem aktif mi
};

DynamicTradeInfo dynamicTrades[100];     // Maksimum 100 işlem takibi
int dynamicTradeCount = 0;
datetime lastSignalBarTime = 0;         // Son sinyal verilen bar zamanı
datetime lastHistoricalBarTime = 0;     // Son geçmiş sinyal bar zamanı

// Bar 1 sinyal bilgisi (AnalyzeSimpleSignals tarafından set edilir)
string bar1SignalType = "";            // Bar 1'de bulunan sinyal türü
string bar1PatternName = "";           // Bar 1'de bulunan pattern adı
bool bar1SignalFound = false;          // Bar 1'de sinyal bulundu mu?

// Trend bilgileri
string currentTrend = "Nötr";
double fastMA_Value = 0;
double slowMA_Value = 0;

// Destek/Direnç bilgileri
double currentSupportLevel = 0;
double currentResistanceLevel = 0;
datetime lastSR_UpdateTime = 0;

// Telegram bilgileri
string lastTrendSent = "";
datetime lastTelegramTime = 0;
string lastSignalSent = "";
datetime lastSignalTime = 0;
datetime lastCommandCheckTime = 0;
int lastUpdateId = 0;

// Auto Trade bilgileri
int dailyTradeCount = 0;
datetime lastTradeDate = 0;
double accountStartBalance = 0;
bool autoTradeEnabled = false;

// Sinyal tekrar engelleme
datetime lastCurrentBarSignalTime = 0;

// SentinelPanel durumu
bool panelCreated = false;

// Panel bilgileri
int totalSignalCount = 0;
int approvedSignalCount = 0;
string lastHistoricalSignal = "Henüz sinyal yok";
datetime lastHistoricalSignalTime = 0;
double dailyStartBalance = 0;
datetime dailyStartTime = 0;

// Sinyal istatistikleri dosya sistemi
string statsFileName = "SENTINEL_Stats_" + _Symbol + ".txt";

// Manuel panel sistemi kullanılıyor - CDialog kodları kaldırıldı

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("🛡️ SENTINEL Trading Sistemi başlatıldı");
    Print("📊 Trend Filtresi: ", UseTrendFilter ? "Açık" : "Kapalı");
    Print("⚡ Tick Bazlı Analiz: ", EnableTickBasedAnalysis ? "Açık" : "Kapalı");
    Print("📱 Telegram Bildirimleri: ", EnableTelegram ? "Açık" : "Kapalı");
    Print("🤖 Auto Trade: ", EnableAutoTrade ? "Açık" : "Kapalı");

    // Auto Trade başlangıç ayarları
    if(EnableAutoTrade)
    {
        autoTradeEnabled = true;
        accountStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        dailyTradeCount = 0;
        lastTradeDate = TimeCurrent();
        Print("💰 Başlangıç Bakiye: ", DoubleToString(accountStartBalance, 2));
        Print("🎯 Risk Yüzdesi: %", DoubleToString(RiskPercent, 1));
        Print("📏 Güvenli Alan Oranı: %", DoubleToString(SafeZoneRatio * 100, 1));
    }

    // Sinyal istatistiklerini dosyadan yükle
    LoadSignalStats();

    // Panel başlangıç ayarları (günlük değerler her başlatmada sıfırlanır)
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    dailyStartTime = TimeCurrent();

    // MA handle'larını her zaman oluştur (trend info için gerekli)
    fastMA_Handle = iMA(_Symbol, _Period, FastMA_Period, 0, MA_Method, MA_Price);
    slowMA_Handle = iMA(_Symbol, _Period, SlowMA_Period, 0, MA_Method, MA_Price);

    if(fastMA_Handle == INVALID_HANDLE || slowMA_Handle == INVALID_HANDLE)
    {
        Print("❌ Hareketli Ortalama göstergeleri oluşturulamadı!");
        return(INIT_FAILED);
    }

    Print("✅ Hareketli Ortalama göstergeleri oluşturuldu - Hızlı:", FastMA_Period, " Yavaş:", SlowMA_Period);

    // MA verilerinin yüklenmesini bekle
    Sleep(100);

    // İlk trend bilgisini hesapla
    if(UseTrendFilter || ShowTrendInfo)
    {
        UpdateTrendInfo();
        Print("✅ İlk trend bilgisi hesaplandı: ", currentTrend);
    }

    // Grafik ayarları
    ChartSetInteger(0, CHART_SHOW_GRID, false);
    ChartSetInteger(0, CHART_FOREGROUND, false);

    // Eski sinyalleri temizle
    ObjectsDeleteAll(0, "SimpleSignal_");
    ObjectsDeleteAll(0, "TrendInfo_");
    ObjectsDeleteAll(0, "SR_");

    // SentinelPanel indicator'ını yükle
    if(ShowTrendInfo)
    {
        // SentinelPanel indicator'ını başlat
        CreateSentinelPanel();
        panelCreated = (sentinelPanelHandle != INVALID_HANDLE);

        if(panelCreated)
            Print("✅ SentinelPanel başarıyla yüklendi");
        else
            Print("❌ SentinelPanel yüklenemedi!");
    }

    // Destek/Direnç seviyelerini hesapla ve çiz
    if(ShowSupportResistance)
    {
        //Print("🔍 Destek/Direnç hesaplaması başlatılıyor...");
        CalculateSupportResistance();
        DrawSupportResistanceLines();
        //Print("✅ Destek/Direnç seviyeleri hesaplandı");
        //Print("📊 Hesaplanan Destek: ", DoubleToString(currentSupportLevel, _Digits));
        //Print("📊 Hesaplanan Direnç: ", DoubleToString(currentResistanceLevel, _Digits));
    }
    else
    {
        Print("⚠️ Destek/Direnç gösterimi kapalı");
    }

    // Geçmiş sinyalleri analiz et ve çiz (piyasa kapalıyken de çalışsın)
    //Print("🔍 Geçmiş sinyal analizi başlatılıyor...");
    AnalyzeSimpleSignals();
     Print("✅ Geçmiş sinyaller çizildi");

    // Object sayısını kontrol et
    int totalObjects = ObjectsTotal(0);
    //Print("📊 Toplam Object Sayısı: ", totalObjects);

    // Destek/Direnç objelerini kontrol et


    // Telegram başlangıç mesajı
    if(EnableTelegram && StringLen(TelegramBotToken) > 0 && StringLen(TelegramChatID) > 0)
    {
        string startMsg = "🛡️ <b>SENTINEL Sistemi Başlatıldı</b>\n\n";
        startMsg += "📊 <b>Sembol:</b> " + _Symbol + "\n";
        startMsg += "⏰ <b>Zaman Dilimi:</b> " + GetTimeframeName(_Period) + "\n";
        startMsg += "🤖 <b>AutoTrade:</b> " + (EnableAutoTrade ? "✅ Açık" : "❌ Kapalı") + "\n";
        startMsg += "📱 <b>Komutlar:</b> " + (EnableTelegramCommands ? "✅ Açık" : "❌ Kapalı") + "\n\n";
        startMsg += "🎯 Sistem hazır ve aktif!\n";
        startMsg += "📱 Aşağıdaki menüden işlemlerinizi yapabilirsiniz.";

        // Ana menü keyboard'u ile gönder
        string keyboard = CreateMainMenuKeyboard();
        SendTelegramMessageWithKeyboard(startMsg, keyboard);
    }

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // SentinelPanel'i temizle
    if(panelCreated)
    {
        CloseSentinelPanel();
        panelCreated = false;
    }

    // Handle'ları temizle
    if(fastMA_Handle != INVALID_HANDLE)
        IndicatorRelease(fastMA_Handle);
    if(slowMA_Handle != INVALID_HANDLE)
        IndicatorRelease(slowMA_Handle);

    // Objeleri temizle
    ObjectsDeleteAll(0, "SimpleSignal_");
    ObjectsDeleteAll(0, "TrendInfo_");
    ObjectsDeleteAll(0, "SR_");

    // Sinyal istatistiklerini kaydet
    SaveSignalStats();

    Print("🛑 Gelişmiş Sinyal Tespit Sistemi durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // SentinelPanel'den gelen mesajları kontrol et
    if(panelCreated)
    {
        CheckPanelMessages();
    }

    // Trend bilgilerini güncelle (trend filter veya trend info paneli için)
    if(UseTrendFilter || ShowTrendInfo)
    {
        UpdateTrendInfo();
    }

    // Dinamik SL/TP yönetimi (her tick'te)
    if(UseDynamicSLTP)
    {
        ManageDynamicSLTP();
    }

    // Yeni bar kontrolü
    bool isNewBar = IsNewBar();

    if(isNewBar)
    {
        // Yeni bar - önceki barları analiz et
        currentBarTickCount = 0;
        lastCurrentBarSignal = "";  // Son sinyal bilgisini resetle
        lastCurrentBarSignalTime = 0;  // Sinyal zamanını resetle
        AnalyzeSimpleSignals();

        // Yeni kapanan bar (bar 1) için AutoTrade kontrolü
        if(EnableAutoTrade && autoTradeEnabled)
        {
            CheckNewBarForAutoTrade();
        }

        // Panel güncellemesi artık SentinelPanel.mq5'te yapılıyor

        // Destek/Direnç seviyelerini güncelle (her yeni bar'da)
        if(ShowSupportResistance)
        {
            CalculateSupportResistance();
            DrawSupportResistanceLines();
        }
    }

    // Aktif bar tick bazlı analiz
    if(EnableCurrentBarSignals && EnableTickBasedAnalysis)
    {
        currentBarTickCount++;

        // Minimum tick sayısına ulaştık mı? (Her tick'te yeniden analiz et)
        if(currentBarTickCount >= MinTicksForSignal)
        {
            AnalyzeCurrentBar(); // Her tick'te güncelle
        }
    }

    // Auto Trade yönetimi
    if(EnableAutoTrade && autoTradeEnabled)
    {
        ManageActiveTrades();
    }

    // Telegram komut kontrolü
    if(EnableTelegram && EnableTelegramCommands)
    {
        CheckTelegramCommands();
    }
}

//+------------------------------------------------------------------+
//| Yeni bar kontrolü                                               |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Trend bilgilerini güncelle                                      |
//+------------------------------------------------------------------+
void UpdateTrendInfo()
{
    // Trend info paneli için her zaman çalışsın
    if(!UseTrendFilter && !ShowTrendInfo) return;

    // Handle'lar geçerli mi kontrol et
    if(fastMA_Handle == INVALID_HANDLE || slowMA_Handle == INVALID_HANDLE)
    {
        Print("❌ MA handle'ları geçersiz");
        return;
    }

    double fastMA[], slowMA[];
    ArraySetAsSeries(fastMA, true);
    ArraySetAsSeries(slowMA, true);

    // MA değerlerini al (retry mekanizması ile)
    int attempts = 0;
    while(attempts < 3)
    {
        if(CopyBuffer(fastMA_Handle, 0, 0, 3, fastMA) > 0 &&
           CopyBuffer(slowMA_Handle, 0, 0, 3, slowMA) > 0)
        {
            break; // Başarılı
        }
        attempts++;
        Sleep(10); // Kısa bekleme
    }

    if(attempts >= 3)
    {
        Print("❌ MA değerleri alınamadı (3 deneme sonrası)");
        currentTrend = "Veri Yok";
        fastMA_Value = 0;
        slowMA_Value = 0;
        return;
    }

    fastMA_Value = fastMA[0];
    slowMA_Value = slowMA[0];

    // Önceki trend durumunu kaydet
    string previousTrend = currentTrend;

    // Trend yönünü belirle
    if(fastMA_Value > slowMA_Value)
    {
        // Yükseliş trendi
        if(fastMA[0] > fastMA[1] && slowMA[0] > slowMA[1])
            currentTrend = "Güçlü Yükseliş";
        else
            currentTrend = "Yükseliş";
    }
    else if(fastMA_Value < slowMA_Value)
    {
        // Düşüş trendi
        if(fastMA[0] < fastMA[1] && slowMA[0] < slowMA[1])
            currentTrend = "Güçlü Düşüş";
        else
            currentTrend = "Düşüş";
    }
    else
    {
        currentTrend = "Nötr";
    }

    // Trend değişimi kontrolü ve Telegram bildirimi
    if(previousTrend != currentTrend && previousTrend != "")
    {
        SendTrendNotification(currentTrend, previousTrend);
    }
}

//+------------------------------------------------------------------+
//| Trend filtresi kontrolü                                         |
//+------------------------------------------------------------------+
bool IsTrendAllowsBuy()
{
    if(!UseTrendFilter) return true;

    return (currentTrend == "Yükseliş" || currentTrend == "Güçlü Yükseliş");
}

//+------------------------------------------------------------------+
//| Trend filtresi kontrolü                                         |
//+------------------------------------------------------------------+
bool IsTrendAllowsSell()
{
    if(!UseTrendFilter) return true;

    return (currentTrend == "Düşüş" || currentTrend == "Güçlü Düşüş");
}

//+------------------------------------------------------------------+
//| Aktif bar analizi                                               |
//+------------------------------------------------------------------+
void AnalyzeCurrentBar()
{
    // Aktif bar (index 0) analizi
    double open0 = iOpen(_Symbol, _Period, 0);
    double high0 = iHigh(_Symbol, _Period, 0);
    double low0 = iLow(_Symbol, _Period, 0);
    double close0 = iClose(_Symbol, _Period, 0);

    // Önceki bar
    double open1 = iOpen(_Symbol, _Period, 1);
    double high1 = iHigh(_Symbol, _Period, 1);
    double low1 = iLow(_Symbol, _Period, 1);
    double close1 = iClose(_Symbol, _Period, 1);

    // Mum boyutu kontrolü
    double candleSize = MathAbs(high0 - low0) / _Point;
    if(candleSize < MinCandleSize) return;

    // Pattern analizleri (aktif bar için)

    // Hammer/Shooting Star (tek bar pattern - opsiyonel trend filtresi)
    if(ShowHammer && IsHammer(open0, high0, low0, close0) && (!UseTrendFilterForLiveSignals || IsTrendAllowsBuy()))
    {
        // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
        if(ShouldGenerateSignal("BUY", 0, false))
        {
            DrawSimpleSignal(0, "ALIŞ", BuySignalColor, "🔨 Çekiç (Aktif Bar) - ALIŞ", true);
            return; // Sinyal bulundu, çık
        }
    }

    if(ShowHammer && IsShootingStar(open0, high0, low0, close0) && (!UseTrendFilterForLiveSignals || IsTrendAllowsSell()))
    {
        // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
        if(ShouldGenerateSignal("SELL", 0, false))
        {
            DrawSimpleSignal(0, "SATIŞ", SellSignalColor, "⭐ Düşen Yıldız (Aktif Bar) - SATIŞ", true);
            return; // Sinyal bulundu, çık
        }
    }

    // Marubozu (tek bar pattern - opsiyonel trend filtresi)
    if(ShowMarubozu && IsBullishMarubozu(open0, high0, low0, close0) && (!UseTrendFilterForLiveSignals || IsTrendAllowsBuy()))
    {
        // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
        if(ShouldGenerateSignal("BUY", 0, false))
        {
            DrawSimpleSignal(0, "ALIŞ", BuySignalColor, "📊 Yükseliş Marubozu (Aktif Bar) - ALIŞ", true);
            return; // Sinyal bulundu, çık
        }
    }

    if(ShowMarubozu && IsBearishMarubozu(open0, high0, low0, close0) && (!UseTrendFilterForLiveSignals || IsTrendAllowsSell()))
    {
        // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
        if(ShouldGenerateSignal("SELL", 0, false))
        {
            DrawSimpleSignal(0, "SATIŞ", SellSignalColor, "📊 Düşüş Marubozu (Aktif Bar) - SATIŞ", true);
            return; // Sinyal bulundu, çık
        }
    }

    // Engulfing (iki bar pattern - aktif bar + önceki bar - opsiyonel trend filtresi)
    if(ShowEngulfing && IsBullishEngulfing(open0, high0, low0, close0, open1, high1, low1, close1) && (!UseTrendFilterForLiveSignals || IsTrendAllowsBuy()))
    {
        // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
        if(ShouldGenerateSignal("BUY", 0, false))
        {
            DrawSimpleSignal(0, "ALIŞ", BuySignalColor, "🔥 Yükseliş Sarmalaması (Aktif Bar) - ALIŞ", true);
            return; // Sinyal bulundu, çık
        }
    }

    if(ShowEngulfing && IsBearishEngulfing(open0, high0, low0, close0, open1, high1, low1, close1) && (!UseTrendFilterForLiveSignals || IsTrendAllowsSell()))
    {
        // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
        if(ShouldGenerateSignal("SELL", 0, false))
        {
            DrawSimpleSignal(0, "SATIŞ", SellSignalColor, "🔥 Düşüş Sarmalaması (Aktif Bar) - SATIŞ", true);
            return; // Sinyal bulundu, çık
        }
    }

    // Hiçbir pattern bulunamadı - sinyal yoksa önceki sinyali temizle
    if(lastCurrentBarSignal != "")
    {
        ObjectsDeleteAll(0, "CurrentBar_0_");
        Print("⚪ AKTİF BAR: Sinyal Yok (Formasyon temizlendi)");
        lastCurrentBarSignal = "";
        // Sinyal yönünü de sıfırla
        lastSignalDirection = "";
        lastSignalBarTime = 0;
    }
}

//+------------------------------------------------------------------+
//| Basit sinyal analiz fonksiyonu                                  |
//+------------------------------------------------------------------+
void AnalyzeSimpleSignals()
{
    // Geçmiş analiz için sinyal yönlerini sıfırla
    lastHistoricalDirection = "";
    lastHistoricalBarTime = 0;

    // Bar 1 sinyal bilgilerini sıfırla
    bar1SignalType = "";
    bar1PatternName = "";
    bar1SignalFound = false;

    // Son 500 barı analiz et (500'den 1'e doğru - sondan başa)
    for(int i = 149; i >= 1; i--)
    {
        // Mum verilerini al
        double open1 = iOpen(_Symbol, _Period, i);
        double high1 = iHigh(_Symbol, _Period, i);
        double low1 = iLow(_Symbol, _Period, i);
        double close1 = iClose(_Symbol, _Period, i);
        
        double open2 = iOpen(_Symbol, _Period, i+1);
        double high2 = iHigh(_Symbol, _Period, i+1);
        double low2 = iLow(_Symbol, _Period, i+1);
        double close2 = iClose(_Symbol, _Period, i+1);
        
        // Mum boyutu kontrolü
        double candleSize = MathAbs(high1 - low1) / _Point;
        if(candleSize < MinCandleSize) continue;
        
        // Basit formasyonları kontrol et
        
        // 1. Sarmalama Formasyonları (Geçmiş barlar - trend filtresi VAR)
        if(ShowEngulfing && IsBullishEngulfing(open1, high1, low1, close1, open2, high2, low2, close2) && (!UseTrendFilter || IsTrendAllowsBuy()))
        {
            // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
            if(ShouldGenerateSignal("BUY", i, true))
            {
                // Bar 1 için sinyal bilgisini kaydet
                if(i == 1)
                {
                    bar1SignalType = "BUY";
                    bar1PatternName = "Bullish Engulfing";
                    bar1SignalFound = true;
                }

                DrawSimpleSignal(i, "ALIŞ", BuySignalColor, "🔥 Yükseliş Sarmalaması - ALIŞ", false);
                // Son geçmiş sinyali kaydet (sadece en son 10 bar için)
                if(i <= 10)
                {
                    lastHistoricalSignal = "🔥 Yükseliş Sarmalaması";
                    lastHistoricalSignalTime = iTime(_Symbol, _Period, i);
                }
            }
        }

        if(ShowEngulfing && IsBearishEngulfing(open1, high1, low1, close1, open2, high2, low2, close2) && (!UseTrendFilter || IsTrendAllowsSell()))
        {
            // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
            if(ShouldGenerateSignal("SELL", i, true))
            {
                // Bar 1 için sinyal bilgisini kaydet
                if(i == 1)
                {
                    bar1SignalType = "SELL";
                    bar1PatternName = "Bearish Engulfing";
                    bar1SignalFound = true;
                }

                DrawSimpleSignal(i, "SATIŞ", SellSignalColor, "🔥 Düşüş Sarmalaması - SATIŞ", false);
            }
        }

        // 2. Çekiç/Düşen Yıldız (Geçmiş barlar - trend filtresi VAR)
        if(ShowHammer && IsHammer(open1, high1, low1, close1) && (!UseTrendFilter || IsTrendAllowsBuy()))
        {
            // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
            if(ShouldGenerateSignal("BUY", i, true))
            {
                // Bar 1 için sinyal bilgisini kaydet
                if(i == 1)
                {
                    bar1SignalType = "BUY";
                    bar1PatternName = "Hammer";
                    bar1SignalFound = true;
                }

                DrawSimpleSignal(i, "ALIŞ", BuySignalColor, "🔨 Çekiç - ALIŞ", false);
            }
        }

        if(ShowHammer && IsShootingStar(open1, high1, low1, close1) && (!UseTrendFilter || IsTrendAllowsSell()))
        {
            // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
            if(ShouldGenerateSignal("SELL", i, true))
            {
                // Bar 1 için sinyal bilgisini kaydet
                if(i == 1)
                {
                    bar1SignalType = "SELL";
                    bar1PatternName = "Shooting Star";
                    bar1SignalFound = true;
                }

                DrawSimpleSignal(i, "SATIŞ", SellSignalColor, "⭐ Düşen Yıldız - SATIŞ", false);
            }
        }

        // 3. Marubozu (Geçmiş barlar - trend filtresi VAR)
        if(ShowMarubozu && IsBullishMarubozu(open1, high1, low1, close1) && (!UseTrendFilter || IsTrendAllowsBuy()))
        {
            // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
            if(ShouldGenerateSignal("BUY", i, true))
            {
                // Bar 1 için sinyal bilgisini kaydet
                if(i == 1)
                {
                    bar1SignalType = "BUY";
                    bar1PatternName = "Bullish Marubozu";
                    bar1SignalFound = true;
                }

                DrawSimpleSignal(i, "ALIŞ", BuySignalColor, "📊 Yükseliş Marubozu - ALIŞ", false);
            }
        }

        if(ShowMarubozu && IsBearishMarubozu(open1, high1, low1, close1) && (!UseTrendFilter || IsTrendAllowsSell()))
        {
            // Sinyal yönü kontrolü - sadece yön değiştiğinde sinyal ver
            if(ShouldGenerateSignal("SELL", i, true))
            {
                // Bar 1 için sinyal bilgisini kaydet
                if(i == 1)
                {
                    bar1SignalType = "SELL";
                    bar1PatternName = "Bearish Marubozu";
                    bar1SignalFound = true;
                }

                DrawSimpleSignal(i, "SATIŞ", SellSignalColor, "📊 Düşüş Marubozu - SATIŞ", false);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Pip cinsinden mum boyutu hesapla                                |
//+------------------------------------------------------------------+
double GetCandleSizeInPips(double high, double low)
{
    double candleSize = high - low;
    double pipValue = _Point;

    // 5 haneli broker için (örn: 1.23456)
    if(_Digits == 5 || _Digits == 3)
        pipValue = _Point * 10;

    double pips = candleSize / pipValue;

    // Debug: Mum boyutunu logla (sadece büyük mumlar için)
    if(pips > MinCandleSize * 2)
    {
       // Print("📏 MUM BOYUTU: ", DoubleToString(pips, 1), " pip (Min: ", DoubleToString(MinCandleSize, 1), " pip)");
    }

    return pips;
}

//+------------------------------------------------------------------+
//| Sinyal yönü değişikliği kontrolü                                |
//+------------------------------------------------------------------+
bool ShouldGenerateSignal(string signalDirection, int barIndex, bool isHistorical = false)
{
    datetime barTime = iTime(_Symbol, _Period, barIndex);

    if(isHistorical)
    {
        // Geçmiş analiz için kontrol
        if(lastHistoricalDirection == signalDirection && lastHistoricalBarTime > 0)
        {
            // Aynı yönde sinyal, üretme
            return false;
        }

        // Yön değişti veya ilk sinyal
        if(lastHistoricalDirection != signalDirection && lastHistoricalDirection != "")
        {
           // Print("📈 GEÇMİŞ SİNYAL YÖN DEĞİŞİMİ: ", lastHistoricalDirection, " → ", signalDirection, " (Bar: ", barIndex, ")");
        }
        lastHistoricalDirection = signalDirection;
        lastHistoricalBarTime = barTime;
        return true;
    }
    else
    {
        // Canlı analiz için kontrol
        if(lastSignalDirection == signalDirection && lastSignalBarTime > 0)
        {
            // Aynı yönde sinyal, üretme
            return false;
        }

        // Yön değişti veya ilk sinyal
        if(lastSignalDirection != signalDirection && lastSignalDirection != "")
        {
            //Print("🔄 CANLI SİNYAL YÖN DEĞİŞİMİ: ", lastSignalDirection, " → ", signalDirection);
        }
        lastSignalDirection = signalDirection;
        lastSignalBarTime = barTime;
        return true;
    }
}

//+------------------------------------------------------------------+
//| Basit sinyal çizme fonksiyonu                                   |
//+------------------------------------------------------------------+
void DrawSimpleSignal(int barIndex, string signalText, color signalColor, string alertText, bool isCurrentBar = false)
{
    string prefix = isCurrentBar ? "CurrentBar_" : "SimpleSignal_";
    string objectName = prefix + IntegerToString(barIndex) + "_" + IntegerToString(GetTickCount());

    datetime time = iTime(_Symbol, _Period, barIndex);
    double price;
    double barRange = iHigh(_Symbol, _Period, barIndex) - iLow(_Symbol, _Period, barIndex);

    if(signalColor == BuySignalColor)
        price = iLow(_Symbol, _Period, barIndex) - (barRange * BuyArrowDistance);
    else
        price = iHigh(_Symbol, _Period, barIndex) + (barRange * SellArrowDistance);

    // Aktif bar sinyalleri için önceki sinyalleri temizle
    if(isCurrentBar && barIndex == 0)
    {
        ObjectsDeleteAll(0, "CurrentBar_0_");
    }

    // Ok işareti oluştur
    if(ObjectCreate(0, objectName, OBJ_ARROW, 0, time, price))
    {
        if(signalColor == SellSignalColor)
        {
            ObjectSetInteger(0, objectName, OBJPROP_ARROWCODE, 234); // Wingdings aşağı ok
            ObjectSetString(0, objectName, OBJPROP_FONT, "Wingdings");
        }
        else
        {
            ObjectSetInteger(0, objectName, OBJPROP_ARROWCODE, 233); // Wingdings yukarı ok
            ObjectSetString(0, objectName, OBJPROP_FONT, "Wingdings");
        }

        ObjectSetInteger(0, objectName, OBJPROP_COLOR, signalColor);
        ObjectSetInteger(0, objectName, OBJPROP_WIDTH, isCurrentBar ? ArrowSize + 1 : ArrowSize);
        ObjectSetInteger(0, objectName, OBJPROP_ZORDER, 3);  // Panel'in altında
        ObjectSetString(0, objectName, OBJPROP_TEXT, signalText);

        // Panel yönetimi artık SentinelPanel.mq5'te yapılıyor

        // Aktif bar sinyalleri için farklı stil
        if(isCurrentBar)
        {
            ObjectSetInteger(0, objectName, OBJPROP_STYLE, STYLE_SOLID);
            ObjectSetInteger(0, objectName, OBJPROP_BACK, false); // Ön planda göster
        }

        // Alert göster
        if(ShowAlerts && (barIndex <= 3 || isCurrentBar))
        {
            string trendInfo = UseTrendFilter ? " [Trend: " + currentTrend + "]" : "";
            Alert(alertText + trendInfo + " - " + _Symbol + " " + EnumToString(_Period));
        }

        // Sadece sinyal değiştiğinde log yaz ve Telegram bildirimi gönder
        if(isCurrentBar)
        {
            string currentSignal = alertText;
            datetime currentTime = TimeCurrent();

            // Sinyal değişti VE en az 30 saniye geçti mi?
            if(lastCurrentBarSignal != currentSignal && (currentTime - lastCurrentBarSignalTime) >= 30)
            {
                string trendInfo = UseTrendFilter ? " (Trend: " + currentTrend + ")" : "";
                Print("⚡ YENİ AKTİF BAR SİNYALİ: ", alertText, trendInfo);
                lastCurrentBarSignal = currentSignal;
                lastCurrentBarSignalTime = currentTime;

                // Telegram bildirimi gönder
                string signalType = (signalColor == BuySignalColor) ? "BUY" : "SELL";

                // Sinyal sayaçlarını güncelle
                totalSignalCount++;
                if(EnableAutoTrade && IsSignalSafe(signalType, 0) && IsSpreadAcceptable())
                {
                    approvedSignalCount++;
                }

                // Son sinyal bilgisini güncelle
                lastHistoricalSignal = signalType + " - " + alertText;
                lastHistoricalSignalTime = TimeCurrent();

                // İstatistikleri dosyaya kaydet
                SaveSignalStats();
                string pattern = alertText;
                StringReplace(pattern, " (Aktif Bar) - ALIŞ", "");
                StringReplace(pattern, " (Aktif Bar) - SATIŞ", "");
                SendSignalNotification(signalType, pattern, price, GetTimeframeName(_Period));

                // Auto Trade kontrolü
                if(EnableAutoTrade && autoTradeEnabled)
                {
                    Print("🤖 AUTOTRADE KONTROL BAŞLADI");
                    Print("   EnableAutoTrade: ", EnableAutoTrade ? "✅" : "❌");
                    Print("   autoTradeEnabled: ", autoTradeEnabled ? "✅" : "❌");

                    bool signalSafe = IsSignalSafe(signalType, 0);
                    bool spreadOK = IsSpreadAcceptable();

                    if(signalSafe && spreadOK)
                    {
                        Print("✅ TÜM KONTROLLER GEÇER - İŞLEM BAŞLATILIYOR");
                        ExecuteAutoTrade(signalType, pattern, price);
                    }
                    else
                    {
                        Print("❌ AUTOTRADE REDDEDİLDİ:");
                        Print("   Sinyal Güvenli: ", signalSafe ? "✅" : "❌");
                        Print("   Spread OK: ", spreadOK ? "✅" : "❌");
                    }
                }
                else
                {
                    Print("⚠️ AUTOTRADE KAPALI:");
                    Print("   EnableAutoTrade: ", EnableAutoTrade ? "✅" : "❌");
                    Print("   autoTradeEnabled: ", autoTradeEnabled ? "✅" : "❌");
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Basit Bullish Engulfing tespit fonksiyonu                       |
//+------------------------------------------------------------------+
bool IsBullishEngulfing(double open1, double high1, double low1, double close1,
                       double open2, double high2, double low2, double close2)
{
    // Önceki mum düşüş mumu olmalı
    if(close2 >= open2) return false;
    
    // Mevcut mum yükseliş mumu olmalı
    if(close1 <= open1) return false;
    
    // Mevcut mum önceki mumu sarmalı
    if(!(open1 <= close2 && close1 >= open2)) return false;

    // Minimum mum boyutu kontrolü
    double currentCandleSizePips = GetCandleSizeInPips(high1, low1);
    if(currentCandleSizePips < MinCandleSize) return false;

    // Basit boyut kontrolü
    double prevBodySize = open2 - close2;
    double currBodySize = close1 - open1;

    // Mevcut mum önceki mumdan büyük olmalı
    return currBodySize > prevBodySize;
}

//+------------------------------------------------------------------+
//| Basit Bearish Engulfing tespit fonksiyonu                       |
//+------------------------------------------------------------------+
bool IsBearishEngulfing(double open1, double high1, double low1, double close1,
                       double open2, double high2, double low2, double close2)
{
    // Önceki mum yükseliş mumu olmalı
    if(close2 <= open2) return false;
    
    // Mevcut mum düşüş mumu olmalı
    if(close1 >= open1) return false;
    
    // Mevcut mum önceki mumu sarmalı
    if(!(open1 >= close2 && close1 <= open2)) return false;

    // Minimum mum boyutu kontrolü
    double currentCandleSizePips = GetCandleSizeInPips(high1, low1);
    if(currentCandleSizePips < MinCandleSize) return false;

    // Basit boyut kontrolü
    double prevBodySize = close2 - open2;
    double currBodySize = open1 - close1;

    // Mevcut mum önceki mumdan büyük olmalı
    return currBodySize > prevBodySize;
}

//+------------------------------------------------------------------+
//| Basit Hammer tespit fonksiyonu                                  |
//+------------------------------------------------------------------+
bool IsHammer(double open, double high, double low, double close)
{
    // Minimum mum boyutu kontrolü
    double candleSizePips = GetCandleSizeInPips(high, low);
    if(candleSizePips < MinCandleSize) return false;

    double bodySize = MathAbs(close - open);
    double lowerShadow = MathMin(open, close) - low;
    double upperShadow = high - MathMax(open, close);
    double totalRange = high - low;

    if(totalRange == 0) return false;
    
    // Basit hammer kriterleri
    // Alt gölge gövdenin en az 1.5 katı olmalı
    if(lowerShadow < bodySize * 1.5) return false;
    
    // Üst gölge küçük olmalı
    if(upperShadow > bodySize * 0.5) return false;
    
    // Alt gölge toplam aralığın en az %40'ı olmalı
    if(lowerShadow / totalRange < 0.4) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Basit Shooting Star tespit fonksiyonu                           |
//+------------------------------------------------------------------+
bool IsShootingStar(double open, double high, double low, double close)
{
    // Minimum mum boyutu kontrolü
    double candleSizePips = GetCandleSizeInPips(high, low);
    if(candleSizePips < MinCandleSize) return false;

    double bodySize = MathAbs(close - open);
    double upperShadow = high - MathMax(open, close);
    double lowerShadow = MathMin(open, close) - low;
    double totalRange = high - low;

    if(totalRange == 0) return false;
    
    // Basit shooting star kriterleri
    // Üst gölge gövdenin en az 1.5 katı olmalı
    if(upperShadow < bodySize * 1.5) return false;
    
    // Alt gölge küçük olmalı
    if(lowerShadow > bodySize * 0.5) return false;
    
    // Üst gölge toplam aralığın en az %40'ı olmalı
    if(upperShadow / totalRange < 0.4) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Basit Bullish Marubozu tespit fonksiyonu                        |
//+------------------------------------------------------------------+
bool IsBullishMarubozu(double open, double high, double low, double close)
{
    // Minimum mum boyutu kontrolü
    double candleSizePips = GetCandleSizeInPips(high, low);
    if(candleSizePips < MinCandleSize) return false;

    // Yükseliş mumu olmalı
    if(close <= open) return false;

    double bodySize = close - open;
    double upperShadow = high - close;
    double lowerShadow = open - low;
    double totalRange = high - low;

    if(totalRange == 0) return false;
    
    // Basit marubozu kriterleri
    // Gölgeler küçük olmalı
    if(upperShadow > totalRange * 0.2) return false;
    if(lowerShadow > totalRange * 0.2) return false;
    
    // Gövde toplam aralığın en az %60'ı olmalı
    if(bodySize / totalRange < 0.6) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Basit Bearish Marubozu tespit fonksiyonu                        |
//+------------------------------------------------------------------+
bool IsBearishMarubozu(double open, double high, double low, double close)
{
    // Minimum mum boyutu kontrolü
    double candleSizePips = GetCandleSizeInPips(high, low);
    if(candleSizePips < MinCandleSize) return false;

    // Düşüş mumu olmalı
    if(close >= open) return false;

    double bodySize = open - close;
    double upperShadow = high - open;
    double lowerShadow = close - low;
    double totalRange = high - low;

    if(totalRange == 0) return false;
    
    // Basit marubozu kriterleri
    // Gölgeler küçük olmalı
    if(upperShadow > totalRange * 0.2) return false;
    if(lowerShadow > totalRange * 0.2) return false;
    
    // Gövde toplam aralığın en az %60'ı olmalı
    if(bodySize / totalRange < 0.6) return false;
    
    return true;
}





//+------------------------------------------------------------------+
//| Gerçek FX Destek ve Direnç seviyelerini hesapla                 |
//+------------------------------------------------------------------+
void CalculateSupportResistance()
{
    if(!ShowSupportResistance) return;

    double currentPrice = iClose(_Symbol, _Period, 0);
    //Print("🔍 Mevcut Fiyat: ", DoubleToString(currentPrice, _Digits));

    // Swing High/Low noktalarını bul
    double swingHighs[];
    double swingLows[];

    FindSwingPoints(swingHighs, swingLows, SR_LookbackPeriod);

    //Print("📊 Bulunan Swing High sayısı: ", ArraySize(swingHighs));
    //Print("📊 Bulunan Swing Low sayısı: ", ArraySize(swingLows));

    double bestSupport = 0;
    double bestResistance = 0;
    int maxSupportStrength = 0;
    int maxResistanceStrength = 0;

    // Swing Low'ları destek için kontrol et
    for(int i = 0; i < ArraySize(swingLows); i++)
    {
        if(swingLows[i] > 0 && swingLows[i] < currentPrice)
        {
            int strength = CalculateLevelStrength(swingLows[i], SR_LookbackPeriod);

            if(strength >= SR_MinTouchCount && strength > maxSupportStrength)
            {
                maxSupportStrength = strength;
                bestSupport = swingLows[i];
            }
        }
    }

    // Swing High'ları direnç için kontrol et
    for(int i = 0; i < ArraySize(swingHighs); i++)
    {
        if(swingHighs[i] > 0 && swingHighs[i] > currentPrice)
        {
            int strength = CalculateLevelStrength(swingHighs[i], SR_LookbackPeriod);

            if(strength >= SR_MinTouchCount && strength > maxResistanceStrength)
            {
                maxResistanceStrength = strength;
                bestResistance = swingHighs[i];
            }
        }
    }

    // Eğer swing noktalardan seviye bulunamazsa, basit yöntem kullan
    if(bestSupport == 0 || bestResistance == 0)
    {
        //Print("⚠️ Swing noktalardan seviye bulunamadı, basit yöntem kullanılıyor...");
        CalculateSimpleSupportResistance(bestSupport, bestResistance, currentPrice, SR_LookbackPeriod);
    }

    // Sonuçları kaydet
    currentSupportLevel = bestSupport;
    currentResistanceLevel = bestResistance;
    lastSR_UpdateTime = TimeCurrent();

    //Print("📊 Destek/Direnç Güncellendi - Destek: ", DoubleToString(currentSupportLevel, _Digits),
    //      " (Güç: ", maxSupportStrength, ") | Direnç: ", DoubleToString(currentResistanceLevel, _Digits),
    //      " (Güç: ", maxResistanceStrength, ")");

    // Telegram bildirimi gönder
    SendSRNotification(currentSupportLevel, currentResistanceLevel);

    // Safe Zone çizgilerini çiz
    if(ShowSafeZones)
    {
        DrawSafeZoneLines();
    }
}

//+------------------------------------------------------------------+
//| Swing High ve Low noktalarını bul                               |
//+------------------------------------------------------------------+
void FindSwingPoints(double &swingHighs[], double &swingLows[], int lookbackPeriod)
{
    ArrayResize(swingHighs, 0);
    ArrayResize(swingLows, 0);

    int swingPeriod = MathMax(2, MathMin(5, lookbackPeriod / 20)); // Dinamik swing period
    //Print("🔍 Swing Period: ", swingPeriod, " bar (Lookback: ", lookbackPeriod, ")");

    for(int i = swingPeriod; i <= lookbackPeriod - swingPeriod; i++)
    {
        double currentHigh = iHigh(_Symbol, _Period, i);
        double currentLow = iLow(_Symbol, _Period, i);

        // Swing High kontrolü
        bool isSwingHigh = true;
        for(int j = 1; j <= swingPeriod; j++)
        {
            if(iHigh(_Symbol, _Period, i-j) >= currentHigh ||
               iHigh(_Symbol, _Period, i+j) >= currentHigh)
            {
                isSwingHigh = false;
                break;
            }
        }

        if(isSwingHigh)
        {
            ArrayResize(swingHighs, ArraySize(swingHighs) + 1);
            swingHighs[ArraySize(swingHighs) - 1] = currentHigh;
        }

        // Swing Low kontrolü
        bool isSwingLow = true;
        for(int j = 1; j <= swingPeriod; j++)
        {
            if(iLow(_Symbol, _Period, i-j) <= currentLow ||
               iLow(_Symbol, _Period, i+j) <= currentLow)
            {
                isSwingLow = false;
                break;
            }
        }

        if(isSwingLow)
        {
            ArrayResize(swingLows, ArraySize(swingLows) + 1);
            swingLows[ArraySize(swingLows) - 1] = currentLow;
        }
    }

    //Print("🔍 Swing Point Tespiti Tamamlandı:");
    //Print("   Lookback Period: ", lookbackPeriod, " bar");
    //Print("   Swing Period: ", swingPeriod, " bar");
    //Print("   Bulunan Swing High: ", ArraySize(swingHighs));
    //Print("   Bulunan Swing Low: ", ArraySize(swingLows));
}

//+------------------------------------------------------------------+
//| Seviye gücünü hesapla (test edilme sayısı)                      |
//+------------------------------------------------------------------+
int CalculateLevelStrength(double level, int lookbackPeriod)
{
    int strength = 0;

    for(int i = 1; i <= lookbackPeriod; i++)
    {
        double high = iHigh(_Symbol, _Period, i);
        double low = iLow(_Symbol, _Period, i);
        double close = iClose(_Symbol, _Period, i);
        double open = iOpen(_Symbol, _Period, i);

        // Seviyeye yaklaşma ve geri dönüş kontrolü
        if(MathAbs(high - level) <= SR_TouchTolerance ||
           MathAbs(low - level) <= SR_TouchTolerance)
        {
            // Seviyeden geri dönüş var mı kontrol et
            if(HasBounceFromLevel(level, i))
            {
                strength++;
            }
        }

        // Seviye kırılma kontrolü (güç azaltır)
        if((low < level - SR_TouchTolerance && close > level + SR_TouchTolerance) ||
           (high > level + SR_TouchTolerance && close < level - SR_TouchTolerance))
        {
            strength--; // Kırılma olursa güç azalt
        }
    }

    return MathMax(0, strength); // Negatif olmaz
}

//+------------------------------------------------------------------+
//| Seviyeden geri dönüş kontrolü                                   |
//+------------------------------------------------------------------+
bool HasBounceFromLevel(double level, int barIndex)
{
    double high = iHigh(_Symbol, _Period, barIndex);
    double low = iLow(_Symbol, _Period, barIndex);
    double close = iClose(_Symbol, _Period, barIndex);
    double open = iOpen(_Symbol, _Period, barIndex);

    // Destek seviyesinden yukarı dönüş
    if(low <= level + SR_TouchTolerance && close > open && close > level)
    {
        return true;
    }

    // Direnç seviyesinden aşağı dönüş
    if(high >= level - SR_TouchTolerance && close < open && close < level)
    {
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| Basit Destek/Direnç hesaplama (fallback method)                 |
//+------------------------------------------------------------------+
void CalculateSimpleSupportResistance(double &support, double &resistance, double currentPrice, int lookbackPeriod)
{
    double highestHigh = 0;
    double lowestLow = 999999;

    //Print("📊 Basit yöntem kullanılıyor - Lookback Period: ", lookbackPeriod, " bar");

    // Belirtilen lookback period'un en yüksek ve en düşük seviyelerini bul
    for(int i = 1; i <= lookbackPeriod; i++)
    {
        double high = iHigh(_Symbol, _Period, i);
        double low = iLow(_Symbol, _Period, i);

        if(high > highestHigh) highestHigh = high;
        if(low < lowestLow) lowestLow = low;
    }

    // Mevcut fiyata göre destek/direnç belirle
    if(support == 0)
    {
        // En düşük seviyeyi destek olarak kullan
        support = lowestLow;
        //Print("🟢 Basit Destek: ", DoubleToString(support, _Digits), " (En düşük seviye)");
    }

    if(resistance == 0)
    {
        // En yüksek seviyeyi direnç olarak kullan
        resistance = highestHigh;
        //Print("🔴 Basit Direnç: ", DoubleToString(resistance, _Digits), " (En yüksek seviye)");
    }
}

//+------------------------------------------------------------------+
//| Destek ve Direnç çizgilerini çiz                                |
//+------------------------------------------------------------------+
void DrawSupportResistanceLines()
{
    if(!ShowSupportResistance) return;

    //Print("🎨 Destek/Direnç çizgileri çiziliyor...");
    //Print("📊 Çizilecek Destek: ", DoubleToString(currentSupportLevel, _Digits));
    //Print("📊 Çizilecek Direnç: ", DoubleToString(currentResistanceLevel, _Digits));

    // Eski çizgileri sil
    ObjectDelete(0, "SR_Support");
    ObjectDelete(0, "SR_Resistance");

    // Grafikteki ilk ve son bar zamanlarını al
    datetime firstBarTime = iTime(_Symbol, _Period, SR_LookbackPeriod - 1);
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    datetime futureTime = currentBarTime + PeriodSeconds(_Period) * 100; // 100 bar ilerisine uzat

    //Print("🕐 Çizgi Zamanları:");
    //Print("   Başlangıç: ", TimeToString(firstBarTime));
    //Print("   Bitiş: ", TimeToString(futureTime));
    //Print("   Mevcut: ", TimeToString(currentBarTime));

    // Destek çizgisi çiz
    if(currentSupportLevel > 0)
    {
        //Print("🟢 Destek çizgisi oluşturuluyor: ", DoubleToString(currentSupportLevel, _Digits));

        // Basit çizgi testi - mevcut fiyattan çiz
        datetime testTime1 = iTime(_Symbol, _Period, 10);
        datetime testTime2 = iTime(_Symbol, _Period, 0);

        if(ObjectCreate(0, "SR_Support", OBJ_HLINE, 0, testTime1, currentSupportLevel))
        {
            ObjectSetInteger(0, "SR_Support", OBJPROP_COLOR, SupportColor);
            ObjectSetInteger(0, "SR_Support", OBJPROP_WIDTH, SR_LineWidth);
            ObjectSetInteger(0, "SR_Support", OBJPROP_STYLE, SR_LineStyle);
            ObjectSetInteger(0, "SR_Support", OBJPROP_RAY_RIGHT, true);
            ObjectSetInteger(0, "SR_Support", OBJPROP_RAY_LEFT, false);
            ObjectSetInteger(0, "SR_Support", OBJPROP_ZORDER, 1);  // Panel'in altında
            ObjectSetString(0, "SR_Support", OBJPROP_TEXT, "Destek: " + DoubleToString(currentSupportLevel, _Digits));
            //Print("✅ Destek çizgisi başarıyla oluşturuldu");
        }
        else
        {
            Print("❌ Destek çizgisi oluşturulamadı! Hata: ", GetLastError());
        }
    }
    else
    {
        Print("⚠️ Destek seviyesi bulunamadı (0 değeri)");
    }

    // Direnç çizgisi çiz
    if(currentResistanceLevel > 0)
    {
        ///Print("🔴 Direnç çizgisi oluşturuluyor: ", DoubleToString(currentResistanceLevel, _Digits));

        datetime testTime1_res = iTime(_Symbol, _Period, 10);
        if(ObjectCreate(0, "SR_Resistance", OBJ_HLINE, 0, testTime1_res, currentResistanceLevel))
        {
            ObjectSetInteger(0, "SR_Resistance", OBJPROP_COLOR, ResistanceColor);
            ObjectSetInteger(0, "SR_Resistance", OBJPROP_WIDTH, SR_LineWidth);
            ObjectSetInteger(0, "SR_Resistance", OBJPROP_STYLE, SR_LineStyle);
            ObjectSetInteger(0, "SR_Resistance", OBJPROP_RAY_RIGHT, true);
            ObjectSetInteger(0, "SR_Resistance", OBJPROP_RAY_LEFT, false);
            ObjectSetInteger(0, "SR_Resistance", OBJPROP_ZORDER, 1);  // Panel'in altında
            ObjectSetString(0, "SR_Resistance", OBJPROP_TEXT, "Direnç: " + DoubleToString(currentResistanceLevel, _Digits));
            //Print("✅ Direnç çizgisi başarıyla oluşturuldu");
        }
        else
        {
            Print("❌ Direnç çizgisi oluşturulamadı! Hata: ", GetLastError());
        }
    }
    else
    {
        Print("⚠️ Direnç seviyesi bulunamadı (0 değeri)");
    }

    // Panel yönetimi artık SentinelPanel.mq5'te yapılıyor
}

//+------------------------------------------------------------------+
//| Safe Zone çizgilerini çiz                                       |
//+------------------------------------------------------------------+
void DrawSafeZoneLines()
{
    if(currentSupportLevel <= 0 || currentResistanceLevel <= 0)
        return;

    // Güvenli alan hesapla
    double sfr = (currentResistanceLevel - currentSupportLevel) * SafeZoneRatio;
    double ds = currentSupportLevel + sfr;   // DownSafe
    double rs = currentResistanceLevel - sfr; // UpSafe

    // Eski safe zone çizgilerini sil
    ObjectDelete(0, "SafeZone_DownSafe");
    ObjectDelete(0, "SafeZone_UpSafe");

    // Grafikteki ilk ve son bar zamanlarını al
    datetime firstBarTime = iTime(_Symbol, _Period, SR_LookbackPeriod - 1);
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    datetime futureTime = currentBarTime + PeriodSeconds(_Period) * 100;

    // DownSafe çizgisi çiz
    if(ObjectCreate(0, "SafeZone_DownSafe", OBJ_HLINE, 0, firstBarTime, ds))
    {
        ObjectSetInteger(0, "SafeZone_DownSafe", OBJPROP_COLOR, DownSafeColor);
        ObjectSetInteger(0, "SafeZone_DownSafe", OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, "SafeZone_DownSafe", OBJPROP_STYLE, SafeZone_LineStyle);
        ObjectSetInteger(0, "SafeZone_DownSafe", OBJPROP_RAY_RIGHT, true);
        ObjectSetInteger(0, "SafeZone_DownSafe", OBJPROP_RAY_LEFT, false);
        ObjectSetInteger(0, "SafeZone_DownSafe", OBJPROP_ZORDER, 2);  // Panel'in altında
        ObjectSetString(0, "SafeZone_DownSafe", OBJPROP_TEXT, "DownSafe: " + DoubleToString(ds, _Digits));
       // Print("✅ DownSafe çizgisi oluşturuldu: ", DoubleToString(ds, _Digits));
    }

    // UpSafe çizgisi çiz
    if(ObjectCreate(0, "SafeZone_UpSafe", OBJ_HLINE, 0, firstBarTime, rs))
    {
        ObjectSetInteger(0, "SafeZone_UpSafe", OBJPROP_COLOR, UpSafeColor);
        ObjectSetInteger(0, "SafeZone_UpSafe", OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, "SafeZone_UpSafe", OBJPROP_STYLE, SafeZone_LineStyle);
        ObjectSetInteger(0, "SafeZone_UpSafe", OBJPROP_RAY_RIGHT, true);
        ObjectSetInteger(0, "SafeZone_UpSafe", OBJPROP_RAY_LEFT, false);
        ObjectSetInteger(0, "SafeZone_UpSafe", OBJPROP_ZORDER, 2);  // Panel'in altında
        ObjectSetString(0, "SafeZone_UpSafe", OBJPROP_TEXT, "UpSafe: " + DoubleToString(rs, _Digits));
       // Print("✅ UpSafe çizgisi oluşturuldu: ", DoubleToString(rs, _Digits));
    }

    //Print("📊 Safe Zone Seviyeleri:");
    //Print("   DownSafe: ", DoubleToString(ds, _Digits));
    //Print("   UpSafe: ", DoubleToString(rs, _Digits));
    //Print("   Safe Zone Genişliği: ", DoubleToString(rs - ds, _Digits), " pip");

    // Panel yönetimi artık SentinelPanel.mq5'te yapılıyor
}



//+------------------------------------------------------------------+
//| Telegram Bildirim Fonksiyonları                                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Telegram mesajı gönder                                          |
//+------------------------------------------------------------------+
bool SendTelegramMessage(string message)
{
    if(!EnableTelegram || StringLen(TelegramBotToken) == 0 || StringLen(TelegramChatID) == 0)
        return false;

    // Komut yanıtları için özel rate limiting
    static datetime lastCommandResponse = 0;
    static datetime lastNormalMessage = 0;

    // Komut yanıtı mı kontrol et
    bool isCommandResponse = (StringFind(message, "AutoTrade") != -1 ||
                             StringFind(message, "SENTINEL") != -1 ||
                             StringFind(message, "✅") != -1 ||
                             StringFind(message, "❌") != -1 ||
                             StringFind(message, "🛑") != -1 ||
                             StringFind(message, "🔴") != -1 ||
                             StringFind(message, "💰") != -1 ||
                             StringFind(message, "📊") != -1);

    if(isCommandResponse)
    {
        // Komut yanıtları için 2 saniye rate limit
        if(TimeCurrent() - lastCommandResponse < 2)
        {
            Print("⚠️ Komut rate limit - mesaj atlandı (", TimeCurrent() - lastCommandResponse, " saniye)");
            return false;
        }
    }
    else
    {
        // Normal mesajlar için 30 saniye rate limit
        if(TimeCurrent() - lastNormalMessage < 30)
        {
            Print("⚠️ Normal mesaj rate limit - mesaj atlandı");
            return false;
        }
    }

    string url = "https://api.telegram.org/bot" + TelegramBotToken + "/sendMessage";
    string postData = "chat_id=" + TelegramChatID + "&text=" + UrlEncode(message) + "&parse_mode=HTML";

    char data[];
    char result[];
    string headers = "Content-Type: application/x-www-form-urlencoded\r\n";

    ArrayResize(data, StringToCharArray(postData, data, 0, WHOLE_ARRAY, CP_UTF8) - 1);

    int timeout = 10000; // 10 saniye timeout
    int res = WebRequest("POST", url, headers, timeout, data, result, headers);

    if(res == 200)
    {
        if(isCommandResponse)
            lastCommandResponse = TimeCurrent();
        else
        {
            lastNormalMessage = TimeCurrent();
            lastTelegramTime = TimeCurrent();
        }
        return true;
    }
    else
    {
        if(res == -1)
            Print("❌ Telegram WebRequest izni yok");
        else if(res == 401)
            Print("❌ Telegram bot token hatalı");
        return false;
    }
}

//+------------------------------------------------------------------+
//| Inline keyboard ile Telegram mesajı gönder                     |
//+------------------------------------------------------------------+
bool SendTelegramMessageWithKeyboard(string message, string keyboard)
{
    if(!EnableTelegram || StringLen(TelegramBotToken) == 0 || StringLen(TelegramChatID) == 0)
        return false;

    string url = "https://api.telegram.org/bot" + TelegramBotToken + "/sendMessage";
    string postData = "chat_id=" + TelegramChatID + "&text=" + UrlEncode(message) + "&parse_mode=HTML&reply_markup=" + keyboard;

    char data[];
    char result[];
    string headers = "Content-Type: application/x-www-form-urlencoded\r\n";

    ArrayResize(data, StringToCharArray(postData, data, 0, WHOLE_ARRAY, CP_UTF8) - 1);

    int timeout = 10000; // 10 saniye timeout
    int res = WebRequest("POST", url, headers, timeout, data, result, headers);

    if(res == 200)
    {
        return true;
    }
    else
    {
        if(res == -1)
            Print("❌ Telegram WebRequest izni yok");
        else if(res == 401)
            Print("❌ Telegram bot token hatalı");
        return false;
    }
}

//+------------------------------------------------------------------+
//| URL Encode fonksiyonu                                           |
//+------------------------------------------------------------------+
string UrlEncode(string text)
{
    // UTF-8 byte array'e çevir
    char utf8[];
    int utf8_len = StringToCharArray(text, utf8, 0, WHOLE_ARRAY, CP_UTF8) - 1;

    string result = "";

    // Her byte'ı encode et
    for(int i = 0; i < utf8_len; i++)
    {
        uchar byte = (uchar)utf8[i];

        // ASCII karakterler (32-126) ve bazı güvenli karakterler
        if((byte >= 32 && byte <= 126) &&
           byte != '%' && byte != '&' && byte != '+' && byte != '=' &&
           byte != '?' && byte != '#' && byte != ' ')
        {
            result += CharToString(byte);
        }
        else
        {
            // Özel karakterleri encode et
            if(byte == ' ')
                result += "%20";
            else if(byte == '\n')
                result += "%0A";
            else if(byte == '&')
                result += "%26";
            else if(byte == '#')
                result += "%23";
            else if(byte == '+')
                result += "%2B";
            else if(byte == '=')
                result += "%3D";
            else if(byte == '?')
                result += "%3F";
            else
            {
                // Diğer tüm karakterleri hex encode et
                result += StringFormat("%%%02X", byte);
            }
        }
    }

    return result;
}

//+------------------------------------------------------------------+
//| Ana menü keyboard'u oluştur                                     |
//+------------------------------------------------------------------+
string CreateMainMenuKeyboard()
{
    string keyboard = "{\"inline_keyboard\":[";
    keyboard += "[{\"text\":\"📊 Durum\",\"callback_data\":\"/status\"}";
    keyboard += ",{\"text\":\"💰 Bakiye\",\"callback_data\":\"/balance\"}]";
    keyboard += ",[{\"text\":\"🤖 AutoTrade\",\"callback_data\":\"/autotrade\"}";
    keyboard += ",{\"text\":\"📈 Pozisyonlar\",\"callback_data\":\"/positions\"}]";
    keyboard += ",[{\"text\":\"🔄 Karda Kapat\",\"callback_data\":\"/close_profit\"}";
    keyboard += ",{\"text\":\"🔴 Hepsini Kapat\",\"callback_data\":\"/close_all\"}]";
    keyboard += ",[{\"text\":\"📚 Yardım\",\"callback_data\":\"/help\"}";
    keyboard += ",{\"text\":\"🔄 Menü\",\"callback_data\":\"/start\"}]";
    keyboard += "]}";
    return keyboard;
}

//+------------------------------------------------------------------+
//| AutoTrade menü keyboard'u oluştur                               |
//+------------------------------------------------------------------+
string CreateAutoTradeKeyboard()
{
    string keyboard = "{\"inline_keyboard\":[";
    if(EnableAutoTrade && autoTradeEnabled)
    {
        keyboard += "[{\"text\":\"🔴 AutoTrade KAPAT\",\"callback_data\":\"/autotrade_off\"}]";
    }
    else
    {
        keyboard += "[{\"text\":\"🟢 AutoTrade AÇ\",\"callback_data\":\"/autotrade_on\"}]";
    }
    keyboard += ",[{\"text\":\"🔙 Ana Menü\",\"callback_data\":\"/start\"}]";
    keyboard += "]}";
    return keyboard;
}

//+------------------------------------------------------------------+
//| Sinyal bildirimi gönder                                         |
//+------------------------------------------------------------------+
void SendSignalNotification(string signalType, string pattern, double price, string timeframe)
{
    if(!EnableTelegram || !SendSignalAlerts)
        return;

    // Aynı sinyal tekrarını engelle
    string currentSignal = signalType + "_" + pattern + "_" + timeframe;
    datetime currentTime = TimeCurrent();

    // Son 5 dakika içinde aynı sinyal gönderildi mi?
    if(lastSignalSent == currentSignal && (currentTime - lastSignalTime) < 300)
    {
        // Aynı sinyal tekrarı engellendi
        return;
    }

    string emoji = (signalType == "BUY") ? "🟢" : "🔴";
    string direction = (signalType == "BUY") ? "ALIŞ" : "SATIŞ";

    string message = emoji + " <b>SENTINEL SİNYAL</b>\n\n";
    message += "📊 <b>Sembol:</b> " + _Symbol + "\n";
    message += "⏰ <b>Zaman:</b> " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES) + "\n";
    message += "🎯 <b>Sinyal:</b> " + direction + "\n";
    message += "📈 <b>Pattern:</b> " + pattern + "\n";
    message += "💰 <b>Fiyat:</b> " + DoubleToString(price, _Digits) + "\n";
    message += "⏱️ <b>Timeframe:</b> " + timeframe;

    if(SendTelegramMessage(message))
    {
        // Başarılı gönderimde son sinyal bilgisini kaydet
        lastSignalSent = currentSignal;
        lastSignalTime = currentTime;
        // Sinyal bildirimi gönderildi
    }
}

//+------------------------------------------------------------------+
//| Destek/Direnç bildirimi gönder                                  |
//+------------------------------------------------------------------+
void SendSRNotification(double support, double resistance)
{
    if(!EnableTelegram || !SendSRAlerts)
        return;

    string message = "📊 <b>SENTINEL S/R GÜNCELLEMESİ</b>\n\n";
    message += "📊 <b>Sembol:</b> " + _Symbol + "\n";
    message += "⏰ <b>Zaman:</b> " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES) + "\n";
    message += "🟢 <b>Destek:</b> " + DoubleToString(support, _Digits) + "\n";
    message += "🔴 <b>Direnç:</b> " + DoubleToString(resistance, _Digits) + "\n";
    message += "📏 <b>Mesafe:</b> " + DoubleToString(resistance - support, _Digits) + " pip";

    SendTelegramMessage(message);
}

//+------------------------------------------------------------------+
//| Trend değişim bildirimi gönder                                  |
//+------------------------------------------------------------------+
void SendTrendNotification(string newTrend, string oldTrend)
{
    if(!EnableTelegram || !SendTrendAlerts)
        return;

    if(newTrend == lastTrendSent)
        return;

    string emoji = "📈";
    if(StringFind(newTrend, "BEARISH") >= 0) emoji = "📉";
    if(StringFind(newTrend, "SIDEWAYS") >= 0) emoji = "↔️";

    string message = emoji + " <b>SENTINEL TREND DEĞİŞİMİ</b>\n\n";
    message += "📊 <b>Sembol:</b> " + _Symbol + "\n";
    message += "⏰ <b>Zaman:</b> " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES) + "\n";
    message += "📈 <b>Yeni Trend:</b> " + newTrend + "\n";
    message += "📉 <b>Önceki Trend:</b> " + oldTrend;

    if(SendTelegramMessage(message))
        lastTrendSent = newTrend;
}

//+------------------------------------------------------------------+
//| Telegram komutlarını kontrol et                                 |
//+------------------------------------------------------------------+
void CheckTelegramCommands()
{
    // Belirli aralıklarla kontrol et
    if(TimeCurrent() - lastCommandCheckTime < CommandCheckInterval)
        return;

    lastCommandCheckTime = TimeCurrent();

    // Telegram komutları kontrol et

    // Telegram API'den güncellemeleri al
    string url = "https://api.telegram.org/bot" + TelegramBotToken + "/getUpdates";
    if(lastUpdateId > 0)
        url += "?offset=" + IntegerToString(lastUpdateId + 1);

    char data[];
    char result[];
    string headers = "";

    int timeout = 10000; // 10 saniye timeout
    int res = WebRequest("GET", url, headers, timeout, data, result, headers);

    //Print("🌐 WebRequest sonucu: ", res);

    if(res == 200)
    {
        string response = CharArrayToString(result);
        //Print("📥 Telegram yanıtı alındı: ", StringLen(response), " karakter");

        // Yanıtın ilk 200 karakterini logla
        string preview = StringLen(response) > 200 ? StringSubstr(response, 0, 200) + "..." : response;
        //Print("📄 Yanıt önizleme: ", preview);

        ProcessTelegramUpdates(response);
    }
    else
    {
        //Print("❌ Telegram komut kontrolü başarısız! HTTP Kodu: ", res);
        //Print("🔍 URL: ", url);

        if(res == -1)
        {
           // Print("⚠️ WebRequest izni kontrol edin:");
           // Print("   Tools → Options → Expert Advisors");
            //Print("   ☑ Allow WebRequest for listed URL:");
            //Print("   https://api.telegram.org");
        }
        else if(res == 401)
        {
            //Print("🔑 Bot token hatalı olabilir: ", TelegramBotToken);
        }
        else if(res == 404)
        {
            //Print("🔍 Bot bulunamadı - token kontrol edin");
        }
    }
}

//+------------------------------------------------------------------+
//| Telegram güncellemelerini işle                                  |
//+------------------------------------------------------------------+
void ProcessTelegramUpdates(string response)
{
    // "ok":true kontrolü
    if(StringFind(response, "\"ok\":true") == -1)
        return;

    // Result array kontrolü
    int resultPos = StringFind(response, "\"result\":[");
    if(resultPos == -1)
        return;

    // Her update'i işle
    int pos = resultPos + 10; // "result":[ uzunluğu

    while(true)
    {
        // Update başlangıcı bul
        int updateStart = StringFind(response, "{\"update_id\":", pos);
        if(updateStart == -1) break;

        // Update ID'yi al
        int updateIdStart = updateStart + 13; // {"update_id": uzunluğu
        int updateIdEnd = StringFind(response, ",", updateIdStart);
        if(updateIdEnd == -1) break;

        string updateIdStr = StringSubstr(response, updateIdStart, updateIdEnd - updateIdStart);
        int updateId = (int)StringToInteger(updateIdStr);

        // Update ID bulundu

        if(updateId > lastUpdateId)
        {
            lastUpdateId = updateId;

            // Callback query ve message objelerini bul
            int messageStart = StringFind(response, "\"message\":{", updateStart);
            int callbackStart = StringFind(response, "\"callback_query\":{", updateStart);

            // Callback query önce kontrol et (öncelik callback_query'de)
            if(callbackStart != -1)
            {
                // Callback query işleme
                int dataStart = StringFind(response, "\"data\":\"", callbackStart);
                if(dataStart != -1)
                {
                    dataStart += 8; // "data":" uzunluğu
                    int dataEnd = StringFind(response, "\"", dataStart);
                    if(dataEnd != -1)
                    {
                        string callbackData = StringSubstr(response, dataStart, dataEnd - dataStart);
                        ProcessTelegramCommand(callbackData);
                    }
                }
            }
            else if(messageStart != -1)
            {
                // Normal mesaj işleme
                int chatStart = StringFind(response, "\"chat\":{\"id\":", messageStart);
                if(chatStart != -1)
                {
                    chatStart += 13; // "chat":{"id": uzunluğu
                    int chatEnd = StringFind(response, ",", chatStart);
                    if(chatEnd != -1)
                    {
                        string chatId = StringSubstr(response, chatStart, chatEnd - chatStart);
                        if(chatId == TelegramChatID)
                        {
                            // Text mesajını bul
                            int textStart = StringFind(response, "\"text\":\"", messageStart);
                            if(textStart != -1)
                            {
                                textStart += 8; // "text":" uzunluğu
                                int textEnd = StringFind(response, "\"", textStart);
                                if(textEnd != -1)
                                {
                                    string command = StringSubstr(response, textStart, textEnd - textStart);
                                    ProcessTelegramCommand(command);
                                }
                            }
                        }
                    }
                }
            }
        }

        // Sonraki update'e geç
        pos = updateStart + 1;

        // Güvenlik için maksimum 10 update işle
        static int processCount = 0;
        processCount++;
        if(processCount > 10)
        {
            processCount = 0;
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Telegram komutunu işle                                          |
//+------------------------------------------------------------------+
void ProcessTelegramCommand(string command)
{
    // Komutları temizle ve küçük harfe çevir
    StringTrimLeft(command);
    StringTrimRight(command);

    // Eğer komut '/' ile başlamıyorsa işleme
    if(StringLen(command) == 0 || StringGetCharacter(command, 0) != '/')
        return;

    StringToLower(command);

    string response = "";

    if(command == "/status" || command == "/durum")
    {
        response = GetSystemStatus();
    }
    else if(command == "/start" || command == "/başlat")
    {
        string welcomeMsg = GetWelcomeMessage();
        string keyboard = CreateMainMenuKeyboard();
        SendTelegramMessageWithKeyboard(welcomeMsg, keyboard);
        response = ""; // Response'u temizle
    }
    else if(command == "/help" || command == "/yardım")
    {
        response = GetHelpMessage();
    }
    else if(command == "/autotrade" || command == "/oto")
    {
        string statusMsg = GetAutoTradeStatus();
        string keyboard = CreateAutoTradeKeyboard();
        SendTelegramMessageWithKeyboard(statusMsg, keyboard);
        response = ""; // Response'u temizle
    }
    else if(command == "/autotrade_on" || command == "/oto_açık")
    {
        string statusMsg;
        if(!EnableAutoTrade)
        {
            statusMsg = "❌ AutoTrade parametresi kapalı. EA ayarlarından açın.";
        }
        else
        {
            autoTradeEnabled = true;
            statusMsg = "✅ AutoTrade etkinleştirildi!";
        }
        string keyboard = CreateAutoTradeKeyboard();
        SendTelegramMessageWithKeyboard(statusMsg, keyboard);
        response = ""; // Response'u temizle
    }
    else if(command == "/autotrade_off" || command == "/oto_kapalı")
    {
        autoTradeEnabled = false;
        string statusMsg = "🛑 AutoTrade durduruldu!";
        string keyboard = CreateAutoTradeKeyboard();
        SendTelegramMessageWithKeyboard(statusMsg, keyboard);
        response = ""; // Response'u temizle
    }
    else if(command == "/close_all" || command == "/hepsini_kapat")
    {
        CloseAllPositions();
        response = "🔴 Tüm pozisyonlar kapatılıyor...";
    }
    else if(command == "/close_profit" || command == "/karda_kapat")
    {
        CloseProfitablePositions();
        response = "✅ Karda olan pozisyonlar kapatılıyor...";
    }
    else if(command == "/balance" || command == "/bakiye")
    {
        response = GetBalanceInfo();
    }
    else if(command == "/positions" || command == "/pozisyonlar")
    {
        response = GetPositionsInfo();
    }
    else if(command == "/signals" || command == "/sinyaller")
    {
        response = GetSignalsInfo();
    }
    else
    {
        response = "❓ Bilinmeyen komut: '" + command + "'\n\n/help yazarak komut listesini görebilirsiniz.";
    }

    if(StringLen(response) > 0)
    {
        // Doğrudan Telegram API çağrısı (rate limit bypass)
        string url = "https://api.telegram.org/bot" + TelegramBotToken + "/sendMessage";
        string postData = "chat_id=" + TelegramChatID + "&text=" + UrlEncode(response) + "&parse_mode=HTML";

        char data[];
        char result[];
        string headers = "Content-Type: application/x-www-form-urlencoded\r\n";

        ArrayResize(data, StringToCharArray(postData, data, 0, WHOLE_ARRAY, CP_UTF8) - 1);

        int timeout = 10000;
        WebRequest("POST", url, headers, timeout, data, result, headers);
    }
}

//+------------------------------------------------------------------+
//| Sistem durumu mesajı                                            |
//+------------------------------------------------------------------+
string GetSystemStatus()
{
    string status = "🛡️ <b>SENTINEL SİSTEM DURUMU</b>\n\n";
    status += "📊 <b>Sembol:</b> " + _Symbol + "\n";
    status += "⏰ <b>Timeframe:</b> " + GetTimeframeName(_Period) + "\n";
    status += "🤖 <b>AutoTrade:</b> " + (autoTradeEnabled ? "✅ Açık" : "❌ Kapalı") + "\n";
    status += "📈 <b>Trend:</b> " + currentTrend + "\n";
    status += "🟢 <b>Destek:</b> " + DoubleToString(currentSupportLevel, _Digits) + "\n";
    status += "🔴 <b>Direnç:</b> " + DoubleToString(currentResistanceLevel, _Digits) + "\n";
    status += "📊 <b>Açık Pozisyon:</b> " + IntegerToString(PositionsTotal()) + "\n";
    status += "💰 <b>Bakiye:</b> " + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2) + "\n";
    status += "📈 <b>Toplam Sinyal:</b> " + IntegerToString(totalSignalCount) + "\n";
    status += "✅ <b>Onaylı Sinyal:</b> " + IntegerToString(approvedSignalCount) + "\n";
    status += "🗓️ <b>Günlük İşlem:</b> " + IntegerToString(dailyTradeCount) + "/" + IntegerToString(MaxDailyTrades);

    return status;
}

//+------------------------------------------------------------------+
//| Hoş geldin mesajı                                               |
//+------------------------------------------------------------------+
string GetWelcomeMessage()
{
    string welcome = "🛡️ <b>SENTINEL Trading Sistemi</b>\n\n";
    welcome += "Hoş geldiniz! Sistem aktif ve hazır.\n\n";
    welcome += "📱 <b>Kullanılabilir Komutlar:</b>\n";
    welcome += "/status - Sistem durumu\n\n";
    welcome += "/help - Yardım menüsü\n\n";
    welcome += "/autotrade_on - AutoTrade aç\n\n";
    welcome += "/autotrade_off - AutoTrade kapat\n\n";
    welcome += "/balance - Bakiye bilgisi\n\n";
    welcome += "/positions - Açık pozisyonlar\n\n";
    welcome += "/close_all - Tüm pozisyonları kapat\n\n";
    welcome += "/close_profit - Karda olanları kapat";

    return welcome;
}

//+------------------------------------------------------------------+
//| Yardım mesajı                                                   |
//+------------------------------------------------------------------+
string GetHelpMessage()
{
    string help = "📚 <b>SENTINEL KOMUT LİSTESİ</b>\n\n";
    help += "🔍 <b>Bilgi Komutları:</b>\n";
    help += "/status - Sistem durumu\n";
    help += "/balance - Bakiye ve P&L\n";
    help += "/positions - Açık pozisyonlar\n";
    help += "/signals - Sinyal istatistikleri\n\n";
    help += "🤖 <b>AutoTrade Komutları:</b>\n";
    help += "/autotrade_on - AutoTrade etkinleştir\n";
    help += "/autotrade_off - AutoTrade durdur\n\n";
    help += "💼 <b>Pozisyon Komutları:</b>\n";
    help += "/close_all - Tüm pozisyonları kapat\n";
    help += "/close_profit - Karda olanları kapat\n\n";
    help += "ℹ️ <b>Diğer:</b>\n";
    help += "/help - Bu yardım menüsü\n";
    help += "/start - Hoş geldin mesajı";

    return help;
}

//+------------------------------------------------------------------+
//| Bakiye bilgisi                                                  |
//+------------------------------------------------------------------+
string GetBalanceInfo()
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double margin = AccountInfoDouble(ACCOUNT_MARGIN);
    double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);

    // Günlük kar/zarar hesaplama (geçmiş kayıtlardan)
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE) + " 00:00:00");
    datetime todayEnd = TimeCurrent();

    // Geçmiş seçimi
    if(!HistorySelect(todayStart, todayEnd))
    {
        // Basit bakiye bilgisi döndür
        string simpleInfo = "💰 <b>HESAP BİLGİLERİ</b>\n\n";
        simpleInfo += "💵 <b>Bakiye:</b> $" + DoubleToString(balance, 2) + "\n";
        simpleInfo += "📊 <b>Equity:</b> $" + DoubleToString(equity, 2) + "\n";
        simpleInfo += "🔒 <b>Kullanılan Margin:</b> $" + DoubleToString(margin, 2) + "\n";
        simpleInfo += "🆓 <b>Serbest Margin:</b> $" + DoubleToString(freeMargin, 2) + "\n";
        simpleInfo += "📊 <b>Margin Level:</b> " + DoubleToString(AccountInfoDouble(ACCOUNT_MARGIN_LEVEL), 2) + "%";
        return simpleInfo;
    }

    double dailyProfit = 0;
    double dailyCommission = 0;
    double dailySwap = 0;
    int dailyTrades = 0;
    int winTrades = 0;
    int lossTrades = 0;

    // Bugünkü deal'ları kontrol et
    int totalDeals = HistoryDealsTotal();

    for(int i = 0; i < totalDeals; i++)
    {
        ulong dealTicket = HistoryDealGetTicket(i);
        if(dealTicket > 0)
        {
            string dealSymbol = HistoryDealGetString(dealTicket, DEAL_SYMBOL);
            long dealEntry = HistoryDealGetInteger(dealTicket, DEAL_ENTRY);
            double dealProfit = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
            double dealCommission = HistoryDealGetDouble(dealTicket, DEAL_COMMISSION);
            double dealSwap = HistoryDealGetDouble(dealTicket, DEAL_SWAP);

            // Sadece çıkış işlemlerini say (DEAL_ENTRY_OUT) ve mevcut sembol
            if(dealEntry == DEAL_ENTRY_OUT && dealSymbol == _Symbol)
            {
                dailyProfit += dealProfit;
                dailyCommission += dealCommission;
                dailySwap += dealSwap;
                dailyTrades++;

                if(dealProfit > 0) winTrades++;
                else if(dealProfit < 0) lossTrades++;
            }
        }
    }

    double totalDailyPL = dailyProfit + dailyCommission + dailySwap;
    double winRate = (dailyTrades > 0) ? (winTrades * 100.0 / dailyTrades) : 0;

    string info = "💰 <b>HESAP BİLGİLERİ</b>\n\n";
    info += "💵 <b>Bakiye:</b> $" + DoubleToString(balance, 2) + "\n";
    info += "📊 <b>Equity:</b> $" + DoubleToString(equity, 2) + "\n";
    info += "🔒 <b>Kullanılan Margin:</b> $" + DoubleToString(margin, 2) + "\n";
    info += "🆓 <b>Serbest Margin:</b> $" + DoubleToString(freeMargin, 2) + "\n";
    info += "📊 <b>Margin Level:</b> " + DoubleToString(AccountInfoDouble(ACCOUNT_MARGIN_LEVEL), 2) + "%\n\n";

    info += "📈 <b>GÜNLÜK PERFORMANS (" + _Symbol + ")</b>\n";
    info += "💰 <b>Toplam P&L:</b> $" + DoubleToString(totalDailyPL, 2) + "\n";
    info += "📊 <b>Kar:</b> $" + DoubleToString(dailyProfit, 2) + "\n";
    info += "💸 <b>Komisyon:</b> $" + DoubleToString(dailyCommission, 2) + "\n";
    info += "🔄 <b>Swap:</b> $" + DoubleToString(dailySwap, 2) + "\n";
    info += "🎯 <b>İşlem Sayısı:</b> " + IntegerToString(dailyTrades) + "\n";
    info += "✅ <b>Kazanan:</b> " + IntegerToString(winTrades) + " | ❌ <b>Kaybeden:</b> " + IntegerToString(lossTrades) + "\n";
    info += "📊 <b>Başarı Oranı:</b> %" + DoubleToString(winRate, 1);

    return info;
}

//+------------------------------------------------------------------+
//| AutoTrade durum bilgisi                                         |
//+------------------------------------------------------------------+
string GetAutoTradeStatus()
{
    string status = "🤖 <b>AUTOTRADE DURUMU</b>\n\n";

    if(EnableAutoTrade)
    {
        if(autoTradeEnabled)
        {
            status += "✅ <b>Durum:</b> Aktif\n";
            status += "📊 <b>Günlük İşlem:</b> " + IntegerToString(dailyTradeCount) + "/" + IntegerToString(MaxDailyTrades) + "\n";
            status += "💰 <b>Risk Yüzdesi:</b> %" + DoubleToString(RiskPercent, 1) + "\n";
            status += "🎯 <b>Risk/Ödül:</b> 1:" + DoubleToString(RiskRewardRatio, 1) + "\n";
            status += "📏 <b>Max Spread:</b> " + DoubleToString(MaxSpread, 1) + " pip\n";
        }
        else
        {
            status += "⏸️ <b>Durum:</b> Durdurulmuş\n";
            status += "ℹ️ Manuel olarak durduruldu\n";
        }
    }
    else
    {
        status += "❌ <b>Durum:</b> Kapalı\n";
        status += "ℹ️ EA ayarlarından etkinleştirin\n";
    }

    return status;
}

//+------------------------------------------------------------------+
//| Pozisyon bilgileri                                              |
//+------------------------------------------------------------------+
string GetPositionsInfo()
{
    string info = "📊 <b>AÇIK POZİSYONLAR</b>\n\n";

    int totalPositions = PositionsTotal();
    if(totalPositions == 0)
    {
        info += "📭 Açık pozisyon bulunmuyor.";
        return info;
    }

    double totalProfit = 0;

    for(int i = 0; i < totalPositions; i++)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double volume = PositionGetDouble(POSITION_VOLUME);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
            double profit = PositionGetDouble(POSITION_PROFIT);
            string comment = PositionGetString(POSITION_COMMENT);

            totalProfit += profit;

            string typeStr = (type == POSITION_TYPE_BUY) ? "🟢 BUY" : "🔴 SELL";
            string profitStr = (profit >= 0) ? "✅ +" : "❌ ";

            info += typeStr + " " + DoubleToString(volume, 2) + " lot\n";
            info += "💰 " + DoubleToString(openPrice, _Digits) + " → " + DoubleToString(currentPrice, _Digits) + "\n";
            info += "📈 " + profitStr + DoubleToString(profit, 2) + "\n";
            info += "🎫 #" + IntegerToString(ticket) + "\n\n";
        }
    }

    info += "💵 <b>Toplam P&L:</b> " + DoubleToString(totalProfit, 2);

    return info;
}

//+------------------------------------------------------------------+
//| Sinyal bilgileri                                                |
//+------------------------------------------------------------------+
string GetSignalsInfo()
{
    string info = "📊 <b>SİNYAL İSTATİSTİKLERİ</b>\n\n";
    info += "📈 <b>Toplam Sinyal:</b> " + IntegerToString(totalSignalCount) + "\n";
    info += "✅ <b>Onaylı Sinyal:</b> " + IntegerToString(approvedSignalCount) + "\n";
    info += "🗓️ <b>Günlük İşlem:</b> " + IntegerToString(dailyTradeCount) + "/" + IntegerToString(MaxDailyTrades) + "\n";
    info += "📊 <b>Onay Oranı:</b> ";

    if(totalSignalCount > 0)
    {
        double approvalRate = (approvedSignalCount * 100.0) / totalSignalCount;
        info += DoubleToString(approvalRate, 1) + "%\n";
    }
    else
    {
        info += "0%\n";
    }

    info += "🎯 <b>Son Trend:</b> " + currentTrend + "\n";
    info += "🟢 <b>Destek:</b> " + DoubleToString(currentSupportLevel, _Digits) + "\n";
    info += "🔴 <b>Direnç:</b> " + DoubleToString(currentResistanceLevel, _Digits);

    return info;
}

//+------------------------------------------------------------------+
//| Timeframe adını Türkçe çevir                                    |
//+------------------------------------------------------------------+
string GetTimeframeName(ENUM_TIMEFRAMES period)
{
    switch(period)
    {
        case PERIOD_M1:  return "1 Dakika";
        case PERIOD_M5:  return "5 Dakika";
        case PERIOD_M15: return "15 Dakika";
        case PERIOD_M30: return "30 Dakika";
        case PERIOD_H1:  return "1 Saat";
        case PERIOD_H4:  return "4 Saat";
        case PERIOD_D1:  return "1 Gün";
        case PERIOD_W1:  return "1 Hafta";
        case PERIOD_MN1: return "1 Ay";
        default:         return EnumToString(period);
    }
}

//+------------------------------------------------------------------+
//| Auto Trade Fonksiyonları                                        |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Sinyal güvenlik kontrolü - Temas bazlı                         |
//+------------------------------------------------------------------+
bool IsSignalSafe(string signalType, int barIndex)
{
    Print("🔍 AUTOTRADE KARAR ANALİZİ - ", signalType, " Sinyali");

    if(currentSupportLevel <= 0 || currentResistanceLevel <= 0)
    {
        Print("❌ KARAR: REDDEDİLDİ - Destek/Direnç seviyeleri hesaplanmamış");
        Print("   Support: ", currentSupportLevel, " | Resistance: ", currentResistanceLevel);
        return false;
    }

    double barLow = iLow(_Symbol, _Period, barIndex);
    double barHigh = iHigh(_Symbol, _Period, barIndex);

    // Güvenli alan hesapla
    double sfr = (currentResistanceLevel - currentSupportLevel) * SafeZoneRatio;
    double ds = currentSupportLevel + sfr;   // DownSafe
    double rs = currentResistanceLevel - sfr; // UpSafe

    Print("📊 Seviye Bilgileri:");
    Print("   Support: ", DoubleToString(currentSupportLevel, _Digits));
    Print("   Resistance: ", DoubleToString(currentResistanceLevel, _Digits));
    Print("   DownSafe: ", DoubleToString(ds, _Digits));
    Print("   UpSafe: ", DoubleToString(rs, _Digits));
    Print("   Bar Range: ", DoubleToString(barLow, _Digits), " - ", DoubleToString(barHigh, _Digits));
    Print("   SafeZone Oranı: %", DoubleToString(SafeZoneRatio * 100, 1));

    if(signalType == "BUY")
    {
        // BUY: Barın herhangi bir bölümü Support ile DownSafe arasında olmalı (kesişim kontrolü)
        bool inSafeZone = (barHigh >= currentSupportLevel && barLow <= ds);

        if(inSafeZone)
        {
            Print("✅ BUY Sinyali Güvenli:");
            Print("   SafeZone Kesişimi: ✅ (Bar'ın bir bölümü ", DoubleToString(currentSupportLevel, _Digits), " - ", DoubleToString(ds, _Digits), " aralığında)");
            Print("   Bar Range: ", DoubleToString(barLow, _Digits), " - ", DoubleToString(barHigh, _Digits));
            return true;
        }
        else
        {
            Print("❌ BUY Sinyali Riskli:");
            Print("   SafeZone Kesişimi Yok: ❌");
            Print("   SafeZone: ", DoubleToString(currentSupportLevel, _Digits), " - ", DoubleToString(ds, _Digits));
            Print("   Bar Range: ", DoubleToString(barLow, _Digits), " - ", DoubleToString(barHigh, _Digits));
            return false;
        }
    }
    else if(signalType == "SELL")
    {
        // SELL: Barın herhangi bir bölümü UpSafe ile Resistance arasında olmalı (kesişim kontrolü)
        bool inSafeZone = (barHigh >= rs && barLow <= currentResistanceLevel);

        if(inSafeZone)
        {
            Print("✅ SELL Sinyali Güvenli:");
            Print("   SafeZone Kesişimi: ✅ (Bar'ın bir bölümü ", DoubleToString(rs, _Digits), " - ", DoubleToString(currentResistanceLevel, _Digits), " aralığında)");
            Print("   Bar Range: ", DoubleToString(barLow, _Digits), " - ", DoubleToString(barHigh, _Digits));
            return true;
        }
        else
        {
            Print("❌ SELL Sinyali Riskli:");
            Print("   SafeZone Kesişimi Yok: ❌");
            Print("   SafeZone: ", DoubleToString(rs, _Digits), " - ", DoubleToString(currentResistanceLevel, _Digits));
            Print("   Bar Range: ", DoubleToString(barLow, _Digits), " - ", DoubleToString(barHigh, _Digits));
            return false;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| Spread kontrolü                                                 |
//+------------------------------------------------------------------+
bool IsSpreadAcceptable()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double spread = ask - bid;

    // Spread'i pip cinsine çevir
    double spreadPips = spread / _Point;
    if(_Digits == 5 || _Digits == 3)
        spreadPips /= 10;

    if(spreadPips > MaxSpread)
    {
        Print("❌ Auto Trade: Spread çok yüksek (", DoubleToString(spreadPips, 1), " > ", DoubleToString(MaxSpread, 1), " pip)");
        return false;
    }

    Print("✅ Spread kabul edilebilir: ", DoubleToString(spreadPips, 1), " pip");
    return true;
}

//+------------------------------------------------------------------+
//| Auto Trade işlem gerçekleştir                                   |
//+------------------------------------------------------------------+
void ExecuteAutoTrade(string signalType, string pattern, double signalPrice)
{
    Print("🚀 AUTOTRADE İŞLEM BAŞLATILIYOR");
    Print("   Sinyal Türü: ", signalType);
    Print("   Pattern: ", pattern);
    Print("   Sinyal Fiyatı: ", DoubleToString(signalPrice, _Digits));

    // Günlük işlem limiti kontrolü
    if(!CheckDailyTradeLimit())
    {
        Print("❌ KARAR: REDDEDİLDİ - Günlük işlem limiti aşıldı (", dailyTradeCount, "/", MaxDailyTrades, ")");
        return;
    }
    Print("✅ Günlük Limit Kontrolü: GEÇER (", dailyTradeCount, "/", MaxDailyTrades, ")");

    // Drawdown kontrolü
    if(!CheckDrawdownLimit())
    {
        Print("❌ KARAR: REDDEDİLDİ - Drawdown limiti aşıldı");
        return;
    }
    Print("✅ Drawdown Kontrolü: GEÇER");

    // Lot size hesapla
    double lotSize = CalculateLotSize(signalType);
    if(lotSize <= 0)
    {
        Print("❌ KARAR: REDDEDİLDİ - Lot size hesaplanamadı");
        return;
    }
    Print("✅ Lot Size Hesaplandı: ", DoubleToString(lotSize, 2));

    // Stop Loss hesapla
    double stopLoss = CalculateStopLoss(signalType, signalPrice);

    // Take Profit hesapla (dolar bazlı TP için lot size gerekli)
    double takeProfit = CalculateTakeProfit(signalType, signalPrice, stopLoss);

    Print("📊 SL/TP Hesaplamaları:");
    Print("   Stop Loss: ", DoubleToString(stopLoss, _Digits));
    Print("   Take Profit: ", DoubleToString(takeProfit, _Digits));
    Print("   Risk/Reward: ", DoubleToString(MathAbs(takeProfit - signalPrice) / MathAbs(signalPrice - stopLoss), 2));

    // İşlem gönder
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = (signalType == "BUY") ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    request.price = (signalType == "BUY") ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    request.sl = 0;  // SL/TP'yi programatik olarak yöneteceğiz
    request.tp = 0;  // SL/TP'yi programatik olarak yöneteceğiz
    request.magic = MagicNumber;
    // SL/TP değerlerini comment'e kaydet (programatik yönetim için)
    request.comment = "SENTINEL_" + pattern + "|SL:" + DoubleToString(stopLoss, _Digits) + "|TP:" + DoubleToString(takeProfit, _Digits);
    // Filling mode'u broker'a göre ayarla
    long filling_mode = SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);
    Print("🔍 Broker Filling Modes: ", filling_mode);

    // En basit filling mode'u kullan
    if((filling_mode & SYMBOL_FILLING_IOC) == SYMBOL_FILLING_IOC)
    {
        request.type_filling = ORDER_FILLING_IOC;
        Print("✅ Filling Mode: ORDER_FILLING_IOC");
    }
    else if((filling_mode & SYMBOL_FILLING_FOK) == SYMBOL_FILLING_FOK)
    {
        request.type_filling = ORDER_FILLING_FOK;
        Print("✅ Filling Mode: ORDER_FILLING_FOK");
    }
    else
    {
        // Varsayılan olarak hiç set etme, broker kendi belirlesin
        Print("⚠️ Filling Mode: Varsayılan (broker belirleyecek)");
    }

    // Minimum stop level kontrolü
    double minStopLevel = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
    double currentPrice = request.price;
    double slDistance = MathAbs(currentPrice - stopLoss);
    double tpDistance = MathAbs(takeProfit - currentPrice);

    Print("🔍 Stop Level Kontrolü:");
    Print("   Minimum Stop Level: ", DoubleToString(minStopLevel, _Digits), " (", DoubleToString(minStopLevel/_Point, 1), " pip)");
    Print("   SL Mesafesi: ", DoubleToString(slDistance, _Digits), " (", DoubleToString(slDistance/_Point, 1), " pip)");
    Print("   TP Mesafesi: ", DoubleToString(tpDistance, _Digits), " (", DoubleToString(tpDistance/_Point, 1), " pip)");

    if(slDistance < minStopLevel)
    {
        Print("❌ HATA: SL mesafesi minimum stop level'dan küçük!");
        Print("   Gerekli: ", DoubleToString(minStopLevel/_Point, 1), " pip");
        Print("   Mevcut: ", DoubleToString(slDistance/_Point, 1), " pip");
        return;
    }

    if(tpDistance < minStopLevel)
    {
        Print("❌ HATA: TP mesafesi minimum stop level'dan küçük!");
        Print("   Gerekli: ", DoubleToString(minStopLevel/_Point, 1), " pip");
        Print("   Mevcut: ", DoubleToString(tpDistance/_Point, 1), " pip");
        return;
    }

    Print("✅ Stop Level Kontrolü: GEÇER");

    Print("📋 OrderSend Parametreleri:");
    Print("   Symbol: ", request.symbol);
    Print("   Type: ", EnumToString(request.type));
    Print("   Volume: ", DoubleToString(request.volume, 2));
    Print("   Price: ", DoubleToString(request.price, _Digits));
    Print("   SL: ", DoubleToString(request.sl, _Digits));
    Print("   TP: ", DoubleToString(request.tp, _Digits));
    Print("   Magic: ", request.magic);
    Print("   Comment: ", request.comment);

    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            dailyTradeCount++;
            Print("✅ Auto Trade Başarılı:");
            Print("   Sinyal: ", signalType);
            Print("   Pattern: ", pattern);
            Print("   Lot: ", DoubleToString(lotSize, 2));
            Print("   Fiyat: ", DoubleToString(request.price, _Digits));
            Print("   SL: ", DoubleToString(stopLoss, _Digits));
            Print("   TP: ", DoubleToString(takeProfit, _Digits));
            Print("   Ticket: ", result.order);

            // Dinamik SL/TP sistemi için kayıt ekle
            if(UseDynamicSLTP)
            {
                datetime signalBarTime = iTime(_Symbol, _Period, 1); // Sinyal barı (önceki kapanan bar)
                AddDynamicTrade(result.order, signalType, signalBarTime);
            }

            // Telegram bildirimi
            SendTradeNotification(signalType, pattern, request.price, lotSize, stopLoss, takeProfit, result.order);
        }
        else
        {
            Print("❌ Auto Trade Hatası: ", result.retcode, " - ", result.comment);
        }
    }
    else
    {
        Print("❌ Auto Trade: OrderSend başarısız - ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Günlük işlem limiti kontrolü                                    |
//+------------------------------------------------------------------+
bool CheckDailyTradeLimit()
{
    datetime currentDate = TimeCurrent();

    // Yeni gün başladıysa sayacı sıfırla
    if(TimeToString(currentDate, TIME_DATE) != TimeToString(lastTradeDate, TIME_DATE))
    {
        dailyTradeCount = 0;
        lastTradeDate = currentDate;
        Print("🗓️ Yeni gün - İşlem sayacı sıfırlandı");
    }

    if(dailyTradeCount >= MaxDailyTrades)
    {
        Print("⚠️ Auto Trade: Günlük işlem limiti aşıldı (", dailyTradeCount, "/", MaxDailyTrades, ")");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Drawdown limiti kontrolü                                        |
//+------------------------------------------------------------------+
bool CheckDrawdownLimit()
{
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double drawdown = ((accountStartBalance - currentBalance) / accountStartBalance) * 100;

    if(drawdown > MaxDrawdown)
    {
        Print("⚠️ Auto Trade: Maksimum drawdown aşıldı (", DoubleToString(drawdown, 2), "% > ", DoubleToString(MaxDrawdown, 2), "%)");
        autoTradeEnabled = false;
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Lot size hesapla (Risk bazlı)                                   |
//+------------------------------------------------------------------+
double CalculateLotSize(string signalType)
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = balance * (RiskPercent / 100.0);

    // Stop loss mesafesini hesapla
    double stopLossDistance = 0;
    if(signalType == "BUY")
    {
        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        stopLossDistance = currentPrice - currentSupportLevel;
    }
    else
    {
        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        stopLossDistance = currentResistanceLevel - currentPrice;
    }

    if(stopLossDistance <= 0)
    {
        Print("❌ Stop loss mesafesi hesaplanamadı");
        return 0;
    }

    // Pip değeri hesapla
    double pipValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    if(_Digits == 5 || _Digits == 3)
        pipValue *= 10;

    // Lot size hesapla
    double lotSize = riskAmount / (stopLossDistance / _Point * pipValue);

    // Minimum ve maksimum lot kontrolü
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lotSize = MathMax(lotSize, minLot);
    lotSize = MathMin(lotSize, maxLot);
    lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

    Print("💰 Lot Size Hesaplama:");
    Print("   Risk Miktarı: $", DoubleToString(riskAmount, 2));
    Print("   SL Mesafesi: ", DoubleToString(stopLossDistance, _Digits));
    Print("   Hesaplanan Lot: ", DoubleToString(lotSize, 2));

    return lotSize;
}

//+------------------------------------------------------------------+
//| Stop Loss hesapla                                               |
//+------------------------------------------------------------------+
double CalculateStopLoss(string signalType, double signalPrice)
{
    double stopLoss = 0;

    Print("💰 SL Hesaplama Modu: ", EnumToString(StopLossMode));

    switch(StopLossMode)
    {
        case SL_SUPPORT_RESISTANCE:
        {
            // Tolerans payı hesaplama (point cinsinden)
            double toleranceDistance = SL_TolerancePoints * _Point;

            if(signalType == "BUY")
            {
                // BUY: Support'un altına tolerans payı ekle
                stopLoss = currentSupportLevel - toleranceDistance;
                Print("   BUY SL = Support - Tolerans: ", DoubleToString(stopLoss, _Digits));
                Print("   Support: ", DoubleToString(currentSupportLevel, _Digits), " | Tolerans: ", DoubleToString(toleranceDistance, _Digits), " (", SL_TolerancePoints, " point)");
            }
            else
            {
                // SELL: Resistance'ın üstüne tolerans payı ekle
                stopLoss = currentResistanceLevel + toleranceDistance;
                Print("   SELL SL = Resistance + Tolerans: ", DoubleToString(stopLoss, _Digits));
                Print("   Resistance: ", DoubleToString(currentResistanceLevel, _Digits), " | Tolerans: ", DoubleToString(toleranceDistance, _Digits), " (", SL_TolerancePoints, " point)");
            }
            break;
        }

        case SL_FIXED_PIPS:
        {
            double pipDistance = FixedSLPips * _Point;
            if(_Digits == 5 || _Digits == 3) pipDistance *= 10;

            if(signalType == "BUY")
            {
                stopLoss = signalPrice - pipDistance;
                Print("   BUY SL = Sinyal - ", FixedSLPips, " pip: ", DoubleToString(stopLoss, _Digits));
            }
            else
            {
                stopLoss = signalPrice + pipDistance;
                Print("   SELL SL = Sinyal + ", FixedSLPips, " pip: ", DoubleToString(stopLoss, _Digits));
            }
            break;
        }

        case SL_ATR_MULTIPLE:
        {
            // ATR hesaplama (basit - son 14 barın range ortalaması)
            double atr = 0;
            for(int i = 1; i <= 14; i++)
            {
                double high = iHigh(_Symbol, _Period, i);
                double low = iLow(_Symbol, _Period, i);
                atr += (high - low);
            }
            atr = atr / 14.0;

            double atrDistance = atr * SL_ATR_Multiplier;

            if(signalType == "BUY")
            {
                stopLoss = signalPrice - atrDistance;
                Print("   BUY SL = Sinyal - (ATR×", SL_ATR_Multiplier, "): ", DoubleToString(stopLoss, _Digits));
            }
            else
            {
                stopLoss = signalPrice + atrDistance;
                Print("   SELL SL = Sinyal + (ATR×", SL_ATR_Multiplier, "): ", DoubleToString(stopLoss, _Digits));
            }
            Print("   ATR: ", DoubleToString(atr, _Digits), " | ATR Distance: ", DoubleToString(atrDistance, _Digits));
            break;
        }
    }

    return NormalizeDouble(stopLoss, _Digits);
}

//+------------------------------------------------------------------+
//| Take Profit hesapla                                             |
//+------------------------------------------------------------------+
double CalculateTakeProfit(string signalType, double signalPrice, double stopLoss)
{
    double takeProfit = 0;

    Print("💰 TP Hesaplama Modu: ", EnumToString(TakeProfitMode));

    switch(TakeProfitMode)
    {
        case TP_RISK_REWARD:
        {
            // Yeni mantık: Teknik seviyelere göre TP hesaplama
            double safeZone = (currentResistanceLevel - currentSupportLevel) * SafeZoneRatio;
            double upSafe = currentResistanceLevel - safeZone;
            double downSafe = currentSupportLevel + safeZone;

            if(signalType == "BUY")
            {
                // BUY TP = (Resistance + UpSafe) / 2
                takeProfit = (currentResistanceLevel + upSafe) / 2.0;
                Print("   BUY TP = (Resistance + UpSafe) / 2: ", DoubleToString(takeProfit, _Digits));
                Print("   Resistance: ", DoubleToString(currentResistanceLevel, _Digits), " | UpSafe: ", DoubleToString(upSafe, _Digits));
            }
            else
            {
                // SELL TP = (Support + DownSafe) / 2
                takeProfit = (currentSupportLevel + downSafe) / 2.0;
                Print("   SELL TP = (Support + DownSafe) / 2: ", DoubleToString(takeProfit, _Digits));
                Print("   Support: ", DoubleToString(currentSupportLevel, _Digits), " | DownSafe: ", DoubleToString(downSafe, _Digits));
            }
            break;
        }

        case TP_FIXED_PIPS:
        {
            double pipDistance = FixedTPPips * _Point;
            if(_Digits == 5 || _Digits == 3) pipDistance *= 10;

            if(signalType == "BUY")
            {
                takeProfit = signalPrice + pipDistance;
                Print("   BUY TP = Sinyal + ", FixedTPPips, " pip: ", DoubleToString(takeProfit, _Digits));
            }
            else
            {
                takeProfit = signalPrice - pipDistance;
                Print("   SELL TP = Sinyal - ", FixedTPPips, " pip: ", DoubleToString(takeProfit, _Digits));
            }
            break;
        }

        case TP_ATR_MULTIPLE:
        {
            // ATR hesaplama (basit - son 14 barın range ortalaması)
            double atr = 0;
            for(int i = 1; i <= 14; i++)
            {
                double high = iHigh(_Symbol, _Period, i);
                double low = iLow(_Symbol, _Period, i);
                atr += (high - low);
            }
            atr = atr / 14.0;

            double atrDistance = atr * TP_ATR_Multiplier;

            if(signalType == "BUY")
            {
                takeProfit = signalPrice + atrDistance;
                Print("   BUY TP = Sinyal + (ATR×", TP_ATR_Multiplier, "): ", DoubleToString(takeProfit, _Digits));
            }
            else
            {
                takeProfit = signalPrice - atrDistance;
                Print("   SELL TP = Sinyal - (ATR×", TP_ATR_Multiplier, "): ", DoubleToString(takeProfit, _Digits));
            }
            Print("   ATR: ", DoubleToString(atr, _Digits), " | ATR Distance: ", DoubleToString(atrDistance, _Digits));
            break;
        }

        case TP_FIXED_DOLLAR:
        {
            // Dolar bazlı TP hesaplama
            double slDistance = MathAbs(signalPrice - stopLoss);
            double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
            double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

            // Lot size'ı al (bu fonksiyon çağrılmadan önce hesaplanmış olmalı)
            double currentLotSize = CalculateLotSize(signalType);

            // Dolar hedefini pip mesafesine çevir
            double dollarPerPip = (tickValue / tickSize) * currentLotSize;
            double tpDistance = FixedTPDollar / dollarPerPip;

            if(signalType == "BUY")
            {
                takeProfit = signalPrice + (tpDistance * _Point);
                Print("   BUY TP = Sinyal + $", FixedTPDollar, " hedef: ", DoubleToString(takeProfit, _Digits));
            }
            else
            {
                takeProfit = signalPrice - (tpDistance * _Point);
                Print("   SELL TP = Sinyal - $", FixedTPDollar, " hedef: ", DoubleToString(takeProfit, _Digits));
            }
            Print("   Dolar/Pip: $", DoubleToString(dollarPerPip, 4), " | TP Mesafesi: ", DoubleToString(tpDistance, 1), " pip");
            Print("   Lot Size: ", DoubleToString(currentLotSize, 2), " | Hedef: $", FixedTPDollar);
            break;
        }
    }

    return NormalizeDouble(takeProfit, _Digits);
}

//+------------------------------------------------------------------+
//| İşlem bildirimi gönder                                          |
//+------------------------------------------------------------------+
void SendTradeNotification(string signalType, string pattern, double price, double lotSize, double stopLoss, double takeProfit, ulong ticket)
{
    Print("📱 Telegram İşlem Bildirimi Kontrolü:");
    Print("   EnableTelegram: ", EnableTelegram ? "✅" : "❌");
    Print("   SendTradeAlerts: ", SendTradeAlerts ? "✅" : "❌");

    if(!EnableTelegram || !SendTradeAlerts)
    {
        Print("❌ Telegram bildirimi gönderilmedi - Ayarlar kapalı");
        return;
    }

    Print("✅ Telegram bildirimi hazırlanıyor...");

    string emoji = (signalType == "BUY") ? "🟢" : "🔴";
    string direction = (signalType == "BUY") ? "ALIŞ" : "SATIŞ";

    string message = emoji + " <b>SENTINEL İŞLEM AÇILDI</b>\n\n";
    message += "📊 <b>Sembol:</b> " + _Symbol + "\n";
    message += "⏰ <b>Zaman:</b> " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES) + "\n";
    message += "🎯 <b>Yön:</b> " + direction + "\n";
    message += "📈 <b>Pattern:</b> " + pattern + "\n";
    message += "💰 <b>Fiyat:</b> " + DoubleToString(price, _Digits) + "\n";
    message += "📦 <b>Lot:</b> " + DoubleToString(lotSize, 2) + "\n";
    message += "🛑 <b>Stop Loss:</b> " + DoubleToString(stopLoss, _Digits) + "\n";
    message += "🎯 <b>Take Profit:</b> " + DoubleToString(takeProfit, _Digits) + "\n";
    message += "🎫 <b>Ticket:</b> " + IntegerToString(ticket) + "\n";
    message += "📊 <b>Risk/Ödül:</b> 1:" + DoubleToString(RiskRewardRatio, 1);

    Print("📤 Telegram mesajı gönderiliyor...");
    Print("   Mesaj uzunluğu: ", StringLen(message), " karakter");

    bool sent = SendTelegramMessage(message);

    if(sent)
        Print("✅ Telegram işlem bildirimi gönderildi");
    else
        Print("❌ Telegram işlem bildirimi gönderilemedi!");
}

//+------------------------------------------------------------------+
//| Aktif işlemleri yönet                                           |
//+------------------------------------------------------------------+
void ManageActiveTrades()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentPrice = (posType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            double volume = PositionGetDouble(POSITION_VOLUME);

            // SL/TP seviyelerini comment'ten oku (programatik yönetim)
            string comment = PositionGetString(POSITION_COMMENT);
            double stopLoss = 0;
            double takeProfit = 0;

            // Comment formatı: SENTINEL_Pattern|SL:value|TP:value
            int slPos = StringFind(comment, "|SL:");
            int tpPos = StringFind(comment, "|TP:");

            if(slPos >= 0 && tpPos >= 0)
            {
                // SL değerini çıkar
                string slStr = StringSubstr(comment, slPos + 4, tpPos - slPos - 4);
                stopLoss = StringToDouble(slStr);

                // TP değerini çıkar (comment sonuna kadar)
                string tpStr = StringSubstr(comment, tpPos + 4);
                // Eğer TP'den sonra başka karakter varsa temizle
                int spacePos = StringFind(tpStr, " ");
                if(spacePos >= 0) tpStr = StringSubstr(tpStr, 0, spacePos);
                takeProfit = StringToDouble(tpStr);

                // SL/TP değerleri comment'ten başarıyla okundu

                // Programatik SL/TP değerleri comment'ten okundu (log azaltıldı)
            }
            else
            {
                // Eski pozisyonlar için sessizce fallback kullan
                if(posType == POSITION_TYPE_BUY)
                {
                    stopLoss = currentSupportLevel;
                    double slDistance = openPrice - stopLoss;
                    takeProfit = openPrice + (slDistance * RiskRewardRatio);
                }
                else
                {
                    stopLoss = currentResistanceLevel;
                    double slDistance = stopLoss - openPrice;
                    takeProfit = openPrice - (slDistance * RiskRewardRatio);
                }
            }

            // SL/TP kontrolü ve güncelleme
            CheckAndUpdateSLTP(ticket, posType, currentPrice, stopLoss, takeProfit);

            // Trailing stop kontrolü
            if(UseTrailingStop)
            {
                Print("⚠️ Trailing Stop geçici olarak devre dışı - Programatik SL/TP ile çelişiyor");
                // ApplyTrailingStop(ticket, posType, currentPrice, openPrice);
            }
        }
    }

    // Kapatılan işlemler için dinamik kayıtları temizle
    if(UseDynamicSLTP)
    {
        CleanClosedDynamicTrades();
    }
}

//+------------------------------------------------------------------+
//| Programatik SL/TP kontrolü ve yönetimi                         |
//+------------------------------------------------------------------+
void CheckAndUpdateSLTP(ulong ticket, ENUM_POSITION_TYPE posType, double currentPrice, double stopLoss, double takeProfit)
{
    // SL kontrolü
    bool shouldCloseSL = false;
    bool shouldCloseTP = false;

    if(posType == POSITION_TYPE_BUY)
    {
        // BUY pozisyonu: Fiyat SL'nin altına düştü mü?
        if(currentPrice <= stopLoss)
        {
            shouldCloseSL = true;
            Print("🛑 BUY Pozisyon SL Tetiklendi:");
            Print("   Ticket: ", ticket);
            Print("   Mevcut Fiyat: ", DoubleToString(currentPrice, _Digits));
            Print("   Stop Loss: ", DoubleToString(stopLoss, _Digits));
        }
        // BUY pozisyonu: Fiyat TP'ye ulaştı mı?
        else if(currentPrice >= takeProfit)
        {
            shouldCloseTP = true;
            Print("🎯 BUY Pozisyon TP Tetiklendi:");
            Print("   Ticket: ", ticket);
            Print("   Mevcut Fiyat: ", DoubleToString(currentPrice, _Digits));
            Print("   Take Profit: ", DoubleToString(takeProfit, _Digits));
        }
    }
    else // SELL pozisyonu
    {
        // SELL pozisyonu: Fiyat SL'nin üstüne çıktı mı?
        if(currentPrice >= stopLoss)
        {
            shouldCloseSL = true;
            Print("🛑 SELL Pozisyon SL Tetiklendi:");
            Print("   Ticket: ", ticket);
            Print("   Mevcut Fiyat: ", DoubleToString(currentPrice, _Digits));
            Print("   Stop Loss: ", DoubleToString(stopLoss, _Digits));
        }
        // SELL pozisyonu: Fiyat TP'ye ulaştı mı?
        else if(currentPrice <= takeProfit)
        {
            shouldCloseTP = true;
            Print("🎯 SELL Pozisyon TP Tetiklendi:");
            Print("   Ticket: ", ticket);
            Print("   Mevcut Fiyat: ", DoubleToString(currentPrice, _Digits));
            Print("   Take Profit: ", DoubleToString(takeProfit, _Digits));
        }
    }

    // Pozisyonu kapat
    if(shouldCloseSL || shouldCloseTP)
    {
        string reason = shouldCloseSL ? "Stop Loss" : "Take Profit";
        double profit = PositionGetDouble(POSITION_PROFIT);

        Print("🔄 Pozisyon kapatılıyor (", reason, "):");
        Print("   Ticket: ", ticket);
        Print("   Mevcut Kar/Zarar: $", DoubleToString(profit, 2));

        // CTrade sınıfı ile pozisyon kapatma
        CTrade trade;
        if(trade.PositionClose(ticket))
        {
            Print("✅ Pozisyon Kapatıldı (", reason, "):");
            Print("   Ticket: ", ticket);
            Print("   Kar/Zarar: $", DoubleToString(profit, 2));
            Print("   Kapanış Fiyatı: ", DoubleToString(currentPrice, _Digits));

            // Telegram bildirimi
            if(EnableTelegram && SendTradeAlerts)
            {
                string message = (shouldCloseTP ? "🎯" : "🛑") + " <b>Pozisyon Kapatıldı</b>\n\n";
                message += "📋 <b>Ticket:</b> " + IntegerToString(ticket) + "\n";
                message += "💰 <b>Kar/Zarar:</b> $" + DoubleToString(profit, 2) + "\n";
                message += "💲 <b>Kapanış:</b> " + DoubleToString(currentPrice, _Digits) + "\n";
                message += "🎯 <b>Sebep:</b> " + reason;

                Print("📤 Pozisyon kapatma bildirimi gönderiliyor...");
                bool sent = SendTelegramMessage(message);

                if(sent)
                    Print("✅ Pozisyon kapatma bildirimi gönderildi");
                else
                    Print("❌ Pozisyon kapatma bildirimi gönderilemedi!");
            }
            else
            {
                Print("❌ Pozisyon kapatma bildirimi gönderilmedi - Telegram ayarları kapalı");
            }
        }
        else
        {
            int error = GetLastError();
            Print("❌ Pozisyon kapatma başarısız: ", error);
            Print("   Ticket: ", ticket);
            Print("   Trade Result Code: ", trade.ResultRetcode());
            Print("   Trade Comment: ", trade.ResultComment());
        }
    }
}

//+------------------------------------------------------------------+
//| Dinamik SL/TP Sistemi - Bar Bazlı SL/TP Yönetimi               |
//+------------------------------------------------------------------+
void ManageDynamicSLTP()
{
    if(!UseDynamicSLTP) return;

    // Açık pozisyonları kontrol et
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol != _Symbol) continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double currentPrice = (posType == POSITION_TYPE_BUY) ?
                             SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                             SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        // Bu işlem için dinamik bilgileri bul
        int tradeIndex = FindDynamicTradeIndex(ticket);
        if(tradeIndex == -1) continue; // Bu işlem dinamik sistemde değil

        // Yeni SL seviyesini hesapla
        double newSL = CalculateDynamicSL(tradeIndex, posType);
        double newTP = CalculateDynamicTP(tradeIndex, posType);

        // SL/TP güncelle
        UpdatePositionSLTP(ticket, newSL, newTP);
    }
}

//+------------------------------------------------------------------+
//| Dinamik işlem kaydı ekle                                        |
//+------------------------------------------------------------------+
void AddDynamicTrade(ulong ticket, string signalType, datetime signalBarTime)
{
    if(dynamicTradeCount >= 100) return; // Maksimum limit

    // Sinyal barının high/low değerlerini al
    int signalBarIndex = iBarShift(_Symbol, _Period, signalBarTime);
    if(signalBarIndex == -1) return;

    double signalBarHigh = iHigh(_Symbol, _Period, signalBarIndex);
    double signalBarLow = iLow(_Symbol, _Period, signalBarIndex);

    // Yeni kayıt ekle
    dynamicTrades[dynamicTradeCount].ticket = ticket;
    dynamicTrades[dynamicTradeCount].signalBarTime = signalBarTime;
    dynamicTrades[dynamicTradeCount].signalBarHigh = signalBarHigh;
    dynamicTrades[dynamicTradeCount].signalBarLow = signalBarLow;
    dynamicTrades[dynamicTradeCount].signalType = signalType;
    dynamicTrades[dynamicTradeCount].isActive = true;

    dynamicTradeCount++;

    Print("📊 Dinamik SL/TP kaydı eklendi:");
    Print("   Ticket: ", ticket);
    Print("   Sinyal: ", signalType);
    Print("   Sinyal Bar High: ", DoubleToString(signalBarHigh, _Digits));
    Print("   Sinyal Bar Low: ", DoubleToString(signalBarLow, _Digits));
}

//+------------------------------------------------------------------+
//| Dinamik işlem indeksini bul                                     |
//+------------------------------------------------------------------+
int FindDynamicTradeIndex(ulong ticket)
{
    for(int i = 0; i < dynamicTradeCount; i++)
    {
        if(dynamicTrades[i].ticket == ticket && dynamicTrades[i].isActive)
            return i;
    }
    return -1;
}

//+------------------------------------------------------------------+
//| Dinamik SL hesapla                                              |
//+------------------------------------------------------------------+
double CalculateDynamicSL(int tradeIndex, ENUM_POSITION_TYPE posType)
{
    if(tradeIndex == -1) return 0;

    double newSL = 0;

    if(posType == POSITION_TYPE_BUY)
    {
        // BUY için: CB-1 barının low'u (canlı bar önceki bar)
        double prevBarLow = iLow(_Symbol, _Period, 1);

        // İlk SL: Sinyal barının low'u
        double signalBarSL = dynamicTrades[tradeIndex].signalBarLow;

        // En yüksek SL'yi kullan (daha güvenli)
        newSL = MathMax(prevBarLow, signalBarSL);
    }
    else if(posType == POSITION_TYPE_SELL)
    {
        // SELL için: CB-1 barının high'ı
        double prevBarHigh = iHigh(_Symbol, _Period, 1);

        // İlk SL: Sinyal barının high'ı
        double signalBarSL = dynamicTrades[tradeIndex].signalBarHigh;

        // En düşük SL'yi kullan (daha güvenli)
        newSL = MathMin(prevBarHigh, signalBarSL);
    }

    return newSL;
}

//+------------------------------------------------------------------+
//| Dinamik TP hesapla                                              |
//+------------------------------------------------------------------+
double CalculateDynamicTP(int tradeIndex, ENUM_POSITION_TYPE posType)
{
    if(tradeIndex == -1) return 0;

    // Mevcut pozisyon fiyatını al
    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    double newTP = 0;

    if(posType == POSITION_TYPE_BUY)
    {
        // BUY için: Açılış + TP point'i
        newTP = openPrice + (DynamicTPPoints * _Point);
    }
    else if(posType == POSITION_TYPE_SELL)
    {
        // SELL için: Açılış - TP point'i
        newTP = openPrice - (DynamicTPPoints * _Point);
    }

    return newTP;
}

//+------------------------------------------------------------------+
//| Pozisyon SL/TP güncelle                                         |
//+------------------------------------------------------------------+
void UpdatePositionSLTP(ulong ticket, double newSL, double newTP)
{
    CTrade trade;

    // Mevcut SL/TP'yi al
    double currentSL = PositionGetDouble(POSITION_SL);
    double currentTP = PositionGetDouble(POSITION_TP);

    // Değişiklik var mı kontrol et
    bool slChanged = (MathAbs(currentSL - newSL) > _Point);
    bool tpChanged = (MathAbs(currentTP - newTP) > _Point);

    if(slChanged || tpChanged)
    {
        if(trade.PositionModify(ticket, newSL, newTP))
        {
            Print("✅ Dinamik SL/TP güncellendi:");
            Print("   Ticket: ", ticket);
            Print("   Yeni SL: ", DoubleToString(newSL, _Digits));
            Print("   Yeni TP: ", DoubleToString(newTP, _Digits));
        }
        else
        {
            Print("❌ Dinamik SL/TP güncelleme başarısız:");
            Print("   Ticket: ", ticket);
            Print("   Hata: ", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| Dinamik işlem kaydını temizle                                   |
//+------------------------------------------------------------------+
void RemoveDynamicTrade(ulong ticket)
{
    for(int i = 0; i < dynamicTradeCount; i++)
    {
        if(dynamicTrades[i].ticket == ticket)
        {
            dynamicTrades[i].isActive = false;
            Print("🗑️ Dinamik SL/TP kaydı temizlendi: ", ticket);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Kapatılan işlemler için dinamik kayıtları temizle               |
//+------------------------------------------------------------------+
void CleanClosedDynamicTrades()
{
    // Açık pozisyonları kontrol et ve kapatılan işlemleri temizle
    for(int i = 0; i < dynamicTradeCount; i++)
    {
        if(!dynamicTrades[i].isActive) continue;

        // Bu ticket hala açık mı kontrol et
        bool positionExists = false;
        for(int j = 0; j < PositionsTotal(); j++)
        {
            string symbol = PositionGetSymbol(j);
            if(symbol == _Symbol)
            {
                ulong ticket = PositionGetInteger(POSITION_TICKET);
                if(ticket == dynamicTrades[i].ticket)
                {
                    positionExists = true;
                    break;
                }
            }
        }

        // Pozisyon kapatılmışsa kaydı temizle
        if(!positionExists)
        {
            dynamicTrades[i].isActive = false;
            Print("🧹 Kapatılan işlem için dinamik kayıt temizlendi: ", dynamicTrades[i].ticket);
        }
    }
}

//+------------------------------------------------------------------+
//| Trailing stop uygula                                            |
//+------------------------------------------------------------------+
void ApplyTrailingStop(ulong ticket, ENUM_POSITION_TYPE posType, double currentPrice, double openPrice)
{
    Print("🚨 HATA: ApplyTrailingStop çağrıldı ama UseTrailingStop = ", UseTrailingStop);
    Print("   Bu fonksiyon çağrılmamalıydı! Kod kontrol edilmeli.");
    Print("   Ticket: ", ticket);

    // Fonksiyonu tamamen devre dışı bırak
    return;

    double currentSL = PositionGetDouble(POSITION_SL);
    double trailingDistance = TrailingDistance * _Point;
    if(_Digits == 5 || _Digits == 3)
        trailingDistance *= 10;

    double newSL = 0;
    bool shouldUpdate = false;

    if(posType == POSITION_TYPE_BUY)
    {
        newSL = currentPrice - trailingDistance;
        if(newSL > currentSL && newSL > openPrice)
        {
            shouldUpdate = true;
        }
    }
    else
    {
        newSL = currentPrice + trailingDistance;
        if((currentSL == 0 || newSL < currentSL) && newSL < openPrice)
        {
            shouldUpdate = true;
        }
    }

    if(shouldUpdate)
    {
        MqlTradeRequest request = {};
        MqlTradeResult result = {};

        request.action = TRADE_ACTION_SLTP;
        request.position = ticket;
        request.sl = NormalizeDouble(newSL, _Digits);
        request.tp = PositionGetDouble(POSITION_TP); // TP'yi koru

        if(OrderSend(request, result))
        {
            if(result.retcode == TRADE_RETCODE_DONE)
            {
                Print("✅ Trailing Stop Güncellendi - Ticket: ", ticket);
                Print("   Yeni SL: ", DoubleToString(newSL, _Digits));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| SentinelPanel'den Gelen Mesajları Kontrol Et                    |
//+------------------------------------------------------------------+
void CheckPanelMessages()
{
    // Karda Olanları Kapat mesajı
    if(GlobalVariableCheck("SENTINEL_CloseProfit"))
    {
        double value = GlobalVariableGet("SENTINEL_CloseProfit");
        if(value == 1)
        {
            Print("📨 SentinelPanel'den mesaj alındı: Karda Olanları Kapat");
            CloseProfitablePositions();
            GlobalVariableDel("SENTINEL_CloseProfit");
        }
    }

    // Tümünü Kapat mesajı
    if(GlobalVariableCheck("SENTINEL_CloseAll"))
    {
        double value = GlobalVariableGet("SENTINEL_CloseAll");
        if(value == 1)
        {
            Print("📨 SentinelPanel'den mesaj alındı: Tümünü Kapat");
            CloseAllPositions();
            GlobalVariableDel("SENTINEL_CloseAll");
        }
    }
}

//+------------------------------------------------------------------+
//| SentinelPanel Güncelleme Fonksiyonu                             |
//+------------------------------------------------------------------+
void UpdateSentinelPanel()
{
    if(!panelCreated) return;

    // SentinelPanel.mq5'e veri göndermek için GlobalVariable kullan
    GlobalVariableSet("SENTINEL_Trend", StringToDouble(currentTrend));
    GlobalVariableSet("SENTINEL_Balance", AccountInfoDouble(ACCOUNT_BALANCE));
    GlobalVariableSet("SENTINEL_DailyPL", AccountInfoDouble(ACCOUNT_BALANCE) - dailyStartBalance);
    GlobalVariableSet("SENTINEL_OpenTrades", PositionsTotal());
    GlobalVariableSet("SENTINEL_TotalSignals", totalSignalCount);
    GlobalVariableSet("SENTINEL_ApprovedSignals", approvedSignalCount);
    GlobalVariableSet("SENTINEL_AutoTrade", (EnableAutoTrade && autoTradeEnabled) ? 1 : 0);
}

//+------------------------------------------------------------------+
//| Sinyal istatistiklerini dosyaya kaydet                          |
//+------------------------------------------------------------------+
void SaveSignalStats()
{
    int fileHandle = FileOpen(statsFileName, FILE_WRITE|FILE_TXT);
    if(fileHandle != INVALID_HANDLE)
    {
        FileWrite(fileHandle, "SENTINEL_STATS_V1");
        FileWrite(fileHandle, IntegerToString(totalSignalCount));
        FileWrite(fileHandle, IntegerToString(approvedSignalCount));
        FileWrite(fileHandle, lastHistoricalSignal);
        FileWrite(fileHandle, IntegerToString(lastHistoricalSignalTime));
        FileWrite(fileHandle, DoubleToString(dailyStartBalance, 2));
        FileWrite(fileHandle, IntegerToString(dailyStartTime));
        FileClose(fileHandle);
        Print("💾 Sinyal istatistikleri kaydedildi: ", statsFileName);
    }
    else
    {
        Print("❌ Sinyal istatistikleri kaydedilemedi: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Sinyal istatistiklerini dosyadan yükle                          |
//+------------------------------------------------------------------+
void LoadSignalStats()
{
    int fileHandle = FileOpen(statsFileName, FILE_READ|FILE_TXT);
    if(fileHandle != INVALID_HANDLE)
    {
        string version = FileReadString(fileHandle);
        if(version == "SENTINEL_STATS_V1")
        {
            totalSignalCount = (int)StringToInteger(FileReadString(fileHandle));
            approvedSignalCount = (int)StringToInteger(FileReadString(fileHandle));
            lastHistoricalSignal = FileReadString(fileHandle);
            lastHistoricalSignalTime = (datetime)StringToInteger(FileReadString(fileHandle));
            dailyStartBalance = StringToDouble(FileReadString(fileHandle));
            dailyStartTime = (datetime)StringToInteger(FileReadString(fileHandle));

            Print("📂 Sinyal istatistikleri yüklendi:");
            Print("   Toplam Sinyal: ", totalSignalCount);
            Print("   Onaylanan Sinyal: ", approvedSignalCount);
            Print("   Son Sinyal: ", lastHistoricalSignal);
            Print("   Günlük Başlangıç: ", TimeToString(dailyStartTime));
        }
        else
        {
            Print("⚠️ Eski format istatistik dosyası - yeni format oluşturulacak");
        }
        FileClose(fileHandle);
    }
    else
    {
        Print("📂 Yeni istatistik dosyası oluşturulacak: ", statsFileName);
        // İlk kez çalışıyor, varsayılan değerler kullanılacak
        totalSignalCount = 0;
        approvedSignalCount = 0;
        lastHistoricalSignal = "Henüz sinyal yok";
        lastHistoricalSignalTime = 0;
    }
}

//+------------------------------------------------------------------+
//| Chart event handler                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    // Panel event'leri artık SentinelPanel.mq5'te yönetiliyor

    // Burada sadece EA'nın kendi event'lerini işle
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        // Buton tıklamaları - SentinelPanel.mq5'ten gelen mesajları dinle
        if(StringFind(sparam, "SENTINEL_Btn") >= 0)
        {
            if(sparam == "SENTINEL_BtnCloseProfit")
            {
                Print("✅ Karda Olanları Kapat butonu tıklandı");
                CloseProfitablePositions();
            }
            else if(sparam == "SENTINEL_BtnCloseAll")
            {
                Print("✅ Tümünü Kapat butonu tıklandı");
                CloseAllPositions();
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Karda olan pozisyonları kapat                                   |
//+------------------------------------------------------------------+
void CloseProfitablePositions()
{
    int closedCount = 0;
    int totalPositions = PositionsTotal();
    int symbolPositions = 0;
    int profitablePositions = 0;

    Print("🔍 Karda Olan Pozisyon Arama:");
    Print("   Toplam Pozisyon: ", totalPositions);
    Print("   Aranan Sembol: ", _Symbol);
    Print("   Aranan Magic: ", MagicNumber);

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string posSymbol = PositionGetSymbol(i);
        long posMagic = PositionGetInteger(POSITION_MAGIC);
        double profit = PositionGetDouble(POSITION_PROFIT);
        ulong ticket = PositionGetInteger(POSITION_TICKET);

        Print("   Pozisyon ", i, ": ", posSymbol, " | Magic: ", posMagic, " | Kar: ", DoubleToString(profit, 2));

        if(posSymbol == _Symbol)
        {
            symbolPositions++;
            if(profit > 0)
            {
                profitablePositions++;
                // Magic number kontrolünü kaldır - tüm karda olan pozisyonları kapat
                if(ClosePosition(ticket))
                {
                    closedCount++;
                    Print("✅ Karda olan pozisyon kapatıldı - Ticket: ", ticket, " Kar: ", DoubleToString(profit, 2));
                }
            }
        }
    }

    Print("📊 Pozisyon Özeti:");
    Print("   ", _Symbol, " Pozisyonları: ", symbolPositions);
    Print("   Karda Olanlar: ", profitablePositions);
    Print("   Kapatılanlar: ", closedCount);

    if(closedCount > 0)
    {
        Print("✅ Toplam ", closedCount, " karda olan pozisyon kapatıldı");
        if(EnableTelegram)
        {
            string message = "✅ <b>SENTINEL - Karda Olanlar Kapatıldı</b>\n\n";
            message += "📊 <b>Kapatılan Pozisyon:</b> " + IntegerToString(closedCount) + "\n";
            message += "⏰ <b>Zaman:</b> " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);
            SendTelegramMessage(message);
        }
    }
    else
    {
        Print("⚠️ Karda olan pozisyon bulunamadı");
    }
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double profit = PositionGetDouble(POSITION_PROFIT);

            if(ClosePosition(ticket))
            {
                closedCount++;
                Print("✅ Pozisyon kapatıldı - Ticket: ", ticket, " P/L: ", DoubleToString(profit, 2));
            }
        }
    }

    if(closedCount > 0)
    {
        Print("✅ Toplam ", closedCount, " pozisyon kapatıldı");
        if(EnableTelegram)
        {
            string message = "🔴 <b>SENTINEL - Tüm Pozisyonlar Kapatıldı</b>\n\n";
            message += "📊 <b>Kapatılan Pozisyon:</b> " + IntegerToString(closedCount) + "\n";
            message += "⏰ <b>Zaman:</b> " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);
            SendTelegramMessage(message);
        }
    }
    else
    {
        Print("⚠️ Kapatılacak pozisyon bulunamadı");
    }
}

//+------------------------------------------------------------------+
//| Pozisyon kapat                                                  |
//+------------------------------------------------------------------+
bool ClosePosition(ulong ticket)
{
    Print("🔄 Pozisyon kapatma denemesi - Ticket: ", ticket);

    if(!PositionSelectByTicket(ticket))
    {
        Print("❌ Pozisyon bulunamadı - Ticket: ", ticket);
        return false;
    }

    string symbol = PositionGetString(POSITION_SYMBOL);
    double volume = PositionGetDouble(POSITION_VOLUME);
    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    Print("📊 Pozisyon Bilgileri:");
    Print("   Symbol: ", symbol);
    Print("   Volume: ", DoubleToString(volume, 2));
    Print("   Type: ", (posType == POSITION_TYPE_BUY) ? "BUY" : "SELL");

    // CTrade sınıfı ile pozisyon kapatma
    CTrade trade;
    bool result = trade.PositionClose(ticket);

    if(result)
    {
        Print("✅ Pozisyon kapatma başarılı - Ticket: ", ticket);
        return true;
    }
    else
    {
        Print("❌ Pozisyon kapatma başarısız - Ticket: ", ticket);
        Print("   Hata Kodu: ", trade.ResultRetcode());
        Print("   Hata Açıklaması: ", trade.ResultComment());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Yeni bar için AutoTrade kontrolü                                |
//+------------------------------------------------------------------+
void CheckNewBarForAutoTrade()
{
    //Print("🔍 YENİ BAR AUTOTRADE KONTROLÜ BAŞLADI");

    // AnalyzeSimpleSignals'den gelen bar 1 sinyal bilgisini kontrol et
    if(!bar1SignalFound)
    {
        Print("⚪ PATTERN YOK: AnalyzeSimpleSignals bar 1'de formasyon bulamadı");
        return;
    }

    // Yeni kapanan bar (bar 1) verilerini al
    double open1 = iOpen(_Symbol, _Period, 1);
    double high1 = iHigh(_Symbol, _Period, 1);
    double low1 = iLow(_Symbol, _Period, 1);
    double close1 = iClose(_Symbol, _Period, 1);

    Print("📊 Bar 1 (Yeni Kapanan): O:", DoubleToString(open1, _Digits),
          " H:", DoubleToString(high1, _Digits),
          " L:", DoubleToString(low1, _Digits),
          " C:", DoubleToString(close1, _Digits));

    // AnalyzeSimpleSignals'den gelen sinyal bilgisini kullan
    string signalType = bar1SignalType;
    string pattern = bar1PatternName;
    double signalPrice = close1;

    Print("🔥 PATTERN TESPİT (AnalyzeSimpleSignals): ", pattern, " → ", signalType);

    // AutoTrade kontrolleri
    Print("🤖 AUTOTRADE KONTROL BAŞLADI");
    Print("   EnableAutoTrade: ", EnableAutoTrade ? "✅" : "❌");
    Print("   autoTradeEnabled: ", autoTradeEnabled ? "✅" : "❌");

    bool signalSafe = IsSignalSafe(signalType, 1);
    bool spreadOK = IsSpreadAcceptable();

    if(signalSafe && spreadOK)
    {
        Print("✅ TÜM KONTROLLER GEÇER - İŞLEM BAŞLATILIYOR");
        ExecuteAutoTrade(signalType, pattern, signalPrice);
    }
    else
    {
        Print("❌ AUTOTRADE REDDEDİLDİ:");
        Print("   Sinyal Güvenli: ", signalSafe ? "✅" : "❌");
        Print("   Spread OK: ", spreadOK ? "✅" : "❌");
    }
}