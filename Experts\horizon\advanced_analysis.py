#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XAUUSD M5 Gelişmiş Karar Matrisi Analizi
En iyi performans gösteren şart kombinasyonlarını bulur
"""

import pandas as pd
import numpy as np
from itertools import combinations

def load_and_prepare_data():
    """Veriyi yükle ve hazırla"""
    print("📊 Gelişmiş analiz başlıyor...")
    
    df = pd.read_csv('XAUUSD_M5_202501020100_202507170555.csv', sep='\t')
    df.columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'TickVol', 'Vol', 'Spread']
    df = df.drop(0).reset_index(drop=True)
    
    # Veri tiplerini düzelt
    for col in ['Open', 'High', 'Low', 'Close', 'TickVol', 'Spread']:
        df[col] = pd.to_numeric(df[col])
    
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
    
    # Gelişmiş metrikler
    df['BarSize'] = (df['High'] - df['Low']) * 10  # pip
    df['BodySize'] = abs(df['Close'] - df['Open']) * 10  # pip
    df['UpperShadow'] = (df['High'] - df[['Open', 'Close']].max(axis=1)) * 10
    df['LowerShadow'] = (df[['Open', 'Close']].min(axis=1) - df['Low']) * 10
    
    # Kapanış pozisyonu
    df['ClosePosition'] = (df['Close'] - df['Low']) / (df['High'] - df['Low']) * 100
    df['ClosePosition'] = df['ClosePosition'].fillna(50)
    
    # Bar türleri
    df['BarType'] = 'DOJI'
    df.loc[df['Close'] > df['Open'], 'BarType'] = 'BULLISH'
    df.loc[df['Close'] < df['Open'], 'BarType'] = 'BEARISH'
    
    # Hedef değişkenler
    df['NextClose'] = df['Close'].shift(-1)
    df['NextMove'] = (df['NextClose'] - df['Close']) * 10  # pip
    df['NextDirection'] = np.where(df['NextMove'] > 2, 'BUY_TARGET',
                          np.where(df['NextMove'] < -2, 'SELL_TARGET', 'NEUTRAL'))
    
    # Volatilite metrikleri
    df['ATR5'] = df['BarSize'].rolling(5).mean()
    df['ATR20'] = df['BarSize'].rolling(20).mean()
    df['VolRatio'] = df['BarSize'] / df['ATR20']
    
    # Momentum metrikleri
    df['Price5'] = df['Close'].rolling(5).mean()
    df['Price20'] = df['Close'].rolling(20).mean()
    df['Momentum'] = (df['Close'] - df['Price5']) * 10
    
    # Ardışık bar sayısı
    df['ConsecutiveBars'] = 1
    for i in range(1, len(df)):
        if df.loc[i, 'BarType'] == df.loc[i-1, 'BarType']:
            df.loc[i, 'ConsecutiveBars'] = df.loc[i-1, 'ConsecutiveBars'] + 1
    
    # Saat kategorileri
    df['Hour'] = df['DateTime'].dt.hour
    df['Session'] = 'ASIAN'
    df.loc[df['Hour'].between(8, 16), 'Session'] = 'LONDON'
    df.loc[df['Hour'].between(14, 22), 'Session'] = 'NY'
    df.loc[df['Hour'].between(20, 23), 'Session'] = 'OVERLAP'
    
    print(f"✅ {len(df)} bar hazırlandı")
    return df

def find_best_patterns(df, min_samples=100, min_success_rate=60):
    """En iyi performans gösteren pattern'leri bul"""
    print(f"🔍 En iyi pattern'ler aranıyor (min {min_success_rate}% başarı, min {min_samples} test)...")
    
    patterns = []
    
    # Pattern 1: Strong Reversal After Consecutive Bars
    for consecutive in [2, 3, 4]:
        for bar_type in ['BULLISH', 'BEARISH']:
            target = 'SELL_TARGET' if bar_type == 'BULLISH' else 'BUY_TARGET'
            
            condition = (
                (df['ConsecutiveBars'] >= consecutive) &
                (df['BarType'] == bar_type) &
                (df['BarSize'] > 8) &
                (df['BarSize'] < 25) &
                (df['VolRatio'] > 1.2)
            )
            
            if condition.sum() >= min_samples:
                success = (df[condition]['NextDirection'] == target).sum()
                total = condition.sum()
                success_rate = (success / total) * 100
                avg_move = df[condition]['NextMove'].mean()
                
                if success_rate >= min_success_rate:
                    patterns.append({
                        'name': f'Reversal_After_{consecutive}_{bar_type}',
                        'success_rate': success_rate,
                        'avg_move': avg_move,
                        'samples': total,
                        'conditions': [
                            f'{consecutive}+ ardışık {bar_type.lower()} bar',
                            'Bar büyüklüğü 8-25 pip',
                            'Volatilite ortalamanın 1.2x üstü'
                        ]
                    })
    
    # Pattern 2: Breakout After Low Volatility
    low_vol_condition = (
        (df['ATR5'] < df['ATR20'] * 0.8) &
        (df['BarSize'] > df['ATR5'] * 1.5) &
        (df['BodySize'] > df['BarSize'] * 0.6)
    )
    
    for target in ['BUY_TARGET', 'SELL_TARGET']:
        if low_vol_condition.sum() >= min_samples:
            success = (df[low_vol_condition]['NextDirection'] == target).sum()
            total = low_vol_condition.sum()
            success_rate = (success / total) * 100
            avg_move = df[low_vol_condition]['NextMove'].mean()
            
            if success_rate >= min_success_rate:
                patterns.append({
                    'name': f'Breakout_After_LowVol_{target}',
                    'success_rate': success_rate,
                    'avg_move': avg_move,
                    'samples': total,
                    'conditions': [
                        'ATR5 < ATR20 * 0.8 (düşük volatilite)',
                        'Bar büyüklüğü > ATR5 * 1.5',
                        'Body > Bar * 0.6 (güçlü bar)'
                    ]
                })
    
    # Pattern 3: Shadow Reversal
    for shadow_type in ['UPPER', 'LOWER']:
        if shadow_type == 'UPPER':
            condition = (
                (df['UpperShadow'] > df['BodySize'] * 2) &
                (df['UpperShadow'] > 5) &
                (df['ClosePosition'] < 40) &
                (df['BarType'] != 'DOJI')
            )
            target = 'SELL_TARGET'
        else:
            condition = (
                (df['LowerShadow'] > df['BodySize'] * 2) &
                (df['LowerShadow'] > 5) &
                (df['ClosePosition'] > 60) &
                (df['BarType'] != 'DOJI')
            )
            target = 'BUY_TARGET'
        
        if condition.sum() >= min_samples:
            success = (df[condition]['NextDirection'] == target).sum()
            total = condition.sum()
            success_rate = (success / total) * 100
            avg_move = df[condition]['NextMove'].mean()
            
            if success_rate >= min_success_rate:
                patterns.append({
                    'name': f'{shadow_type}_Shadow_Reversal',
                    'success_rate': success_rate,
                    'avg_move': avg_move,
                    'samples': total,
                    'conditions': [
                        f'{shadow_type.lower()} shadow > body * 2',
                        f'{shadow_type.lower()} shadow > 5 pip',
                        f'Kapanış {"alt" if shadow_type == "UPPER" else "üst"} bölgede'
                    ]
                })
    
    # Pattern 4: Session-Based Patterns
    for session in ['LONDON', 'NY', 'OVERLAP']:
        session_data = df[df['Session'] == session]
        
        # Momentum continuation in active sessions
        momentum_condition = (
            (session_data['Momentum'].abs() > 3) &
            (session_data['BarSize'] > 10) &
            (session_data['BodySize'] > session_data['BarSize'] * 0.5)
        )
        
        for direction in ['positive', 'negative']:
            if direction == 'positive':
                final_condition = momentum_condition & (session_data['Momentum'] > 0)
                target = 'BUY_TARGET'
            else:
                final_condition = momentum_condition & (session_data['Momentum'] < 0)
                target = 'SELL_TARGET'
            
            if final_condition.sum() >= min_samples:
                success = (session_data[final_condition]['NextDirection'] == target).sum()
                total = final_condition.sum()
                success_rate = (success / total) * 100
                avg_move = session_data[final_condition]['NextMove'].mean()
                
                if success_rate >= min_success_rate:
                    patterns.append({
                        'name': f'{session}_Momentum_{direction}',
                        'success_rate': success_rate,
                        'avg_move': avg_move,
                        'samples': total,
                        'conditions': [
                            f'{session} seansı',
                            f'Momentum {"pozitif" if direction == "positive" else "negatif"} > 3 pip',
                            'Bar büyüklüğü > 10 pip',
                            'Body > Bar * 0.5'
                        ]
                    })
    
    return sorted(patterns, key=lambda x: x['success_rate'], reverse=True)

def generate_mql5_code(patterns):
    """En iyi pattern'ler için MQL5 kodu oluştur"""
    print("\n💻 MQL5 Kodları Oluşturuluyor...")
    
    mql5_code = """
//+------------------------------------------------------------------+
//| Pattern-Based Signal Functions                                   |
//+------------------------------------------------------------------+

// Bar metrikleri hesapla
double CalculateBarSize(int bar_index)
{
    return (iHigh(_Symbol, _Period, bar_index) - iLow(_Symbol, _Period, bar_index)) / _Point;
}

double CalculateBodySize(int bar_index)
{
    return MathAbs(iClose(_Symbol, _Period, bar_index) - iOpen(_Symbol, _Period, bar_index)) / _Point;
}

double CalculateUpperShadow(int bar_index)
{
    double high = iHigh(_Symbol, _Period, bar_index);
    double open = iOpen(_Symbol, _Period, bar_index);
    double close = iClose(_Symbol, _Period, bar_index);
    return (high - MathMax(open, close)) / _Point;
}

double CalculateLowerShadow(int bar_index)
{
    double low = iLow(_Symbol, _Period, bar_index);
    double open = iOpen(_Symbol, _Period, bar_index);
    double close = iClose(_Symbol, _Period, bar_index);
    return (MathMin(open, close) - low) / _Point;
}

int GetConsecutiveBars(int bar_index)
{
    double current_close = iClose(_Symbol, _Period, bar_index);
    double current_open = iOpen(_Symbol, _Period, bar_index);
    bool current_bullish = current_close > current_open;
    
    int count = 1;
    for(int i = bar_index + 1; i < bar_index + 10; i++)
    {
        double close = iClose(_Symbol, _Period, i);
        double open = iOpen(_Symbol, _Period, i);
        bool bullish = close > open;
        
        if(bullish == current_bullish)
            count++;
        else
            break;
    }
    return count;
}

"""
    
    # En iyi 3 pattern için kod oluştur
    for i, pattern in enumerate(patterns[:3]):
        pattern_name = pattern['name'].replace(' ', '_').replace('-', '_')
        
        mql5_code += f"""
//+------------------------------------------------------------------+
//| Pattern: {pattern['name']} (Success: {pattern['success_rate']:.1f}%)
//+------------------------------------------------------------------+
bool Check_{pattern_name}(int bar_index)
{{
    // Pattern koşulları buraya eklenecek
    // Başarı oranı: {pattern['success_rate']:.1f}%
    // Ortalama hareket: {pattern['avg_move']:.1f} pip
    // Test sayısı: {pattern['samples']}
    
"""
        
        for condition in pattern['conditions']:
            mql5_code += f"    // {condition}\n"
        
        mql5_code += """    
    return false; // Koşulları implement edin
}

"""
    
    return mql5_code

def main():
    """Ana analiz"""
    df = load_and_prepare_data()
    
    # En iyi pattern'leri bul
    best_patterns = find_best_patterns(df, min_samples=50, min_success_rate=55)
    
    print(f"\n🏆 EN İYİ {len(best_patterns)} PATTERN BULUNDU:")
    print("="*80)
    
    for i, pattern in enumerate(best_patterns[:10], 1):
        print(f"\n{i}. 🎯 {pattern['name']}")
        print(f"   ✅ Başarı Oranı: {pattern['success_rate']:.1f}%")
        print(f"   📈 Ortalama Hareket: {pattern['avg_move']:.1f} pip")
        print(f"   📊 Test Sayısı: {pattern['samples']}")
        print("   📋 Koşullar:")
        for condition in pattern['conditions']:
            print(f"      • {condition}")
    
    # MQL5 kodu oluştur
    mql5_code = generate_mql5_code(best_patterns)
    
    # Dosyaya kaydet
    with open('pattern_functions.mq5', 'w', encoding='utf-8') as f:
        f.write(mql5_code)
    
    print(f"\n💾 MQL5 kodları 'pattern_functions.mq5' dosyasına kaydedildi")
    print(f"✅ Toplam {len(df)} bar analiz edildi")

if __name__ == "__main__":
    main()
