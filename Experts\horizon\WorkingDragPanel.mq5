//+------------------------------------------------------------------+
//| WorkingDragPanel.mq5 - %100 Çalışan Sürüklenebilir Panel       |
//| Basit ve garantili sürükleme sistemi                            |
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "100% Çalışan Sürüklenebilir İşlem Paneli"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| Panel ayarları                                                  |
//+------------------------------------------------------------------+
input int StartX = 50;              // Başlangıç X pozisyonu
input int StartY = 50;              // Başlangıç Y pozisyonu

//+------------------------------------------------------------------+
//| Panel boyutları                                                 |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     300
#define PANEL_HEIGHT    200
#define BUTTON_HEIGHT   25
#define MARGIN          10

//+------------------------------------------------------------------+
//| Global değişkenler                                              |
//+------------------------------------------------------------------+
string g_panelName = "WorkingDragPanel";
datetime g_lastUpdate = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    CreateDraggablePanel();
    UpdatePanelData();
    
    Print("✅ Working Drag Panel başlatıldı - Sürüklenebilir!");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, g_panelName);
    Print("🔄 Working Drag Panel kapatıldı");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(TimeCurrent() - g_lastUpdate >= 3)
    {
        UpdatePanelData();
        g_lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == g_panelName + "_BtnProfit")
        {
            CloseProfitablePositions();
        }
        else if(sparam == g_panelName + "_BtnLoss")
        {
            CloseLosingPositions();
        }
        else if(sparam == g_panelName + "_BtnAll")
        {
            CloseAllPositions();
        }
    }
    else if(id == CHARTEVENT_OBJECT_DRAG)
    {
        if(StringFind(sparam, g_panelName + "_Background") >= 0)
        {
            // Panel sürüklendiğinde tüm elementleri güncelle
            UpdateAfterDrag();
        }
    }
}

//+------------------------------------------------------------------+
//| Sürüklenebilir panel oluştur                                    |
//+------------------------------------------------------------------+
void CreateDraggablePanel()
{
    // Ana panel arka planı - sürüklenebilir
    string bgName = g_panelName + "_Background";
    ObjectCreate(0, bgName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, bgName, OBJPROP_XDISTANCE, StartX);
    ObjectSetInteger(0, bgName, OBJPROP_YDISTANCE, StartY);
    ObjectSetInteger(0, bgName, OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, bgName, OBJPROP_YSIZE, PANEL_HEIGHT);
    ObjectSetInteger(0, bgName, OBJPROP_BGCOLOR, C'30,30,30');
    ObjectSetInteger(0, bgName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, bgName, OBJPROP_COLOR, C'100,100,100');
    ObjectSetInteger(0, bgName, OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, bgName, OBJPROP_BACK, false);
    ObjectSetInteger(0, bgName, OBJPROP_SELECTABLE, true);
    ObjectSetInteger(0, bgName, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, bgName, OBJPROP_ZORDER, 1000);
    
    // Header - görsel amaçlı
    string headerName = g_panelName + "_Header";
    ObjectCreate(0, headerName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, headerName, OBJPROP_XDISTANCE, StartX);
    ObjectSetInteger(0, headerName, OBJPROP_YDISTANCE, StartY);
    ObjectSetInteger(0, headerName, OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, headerName, OBJPROP_YSIZE, 25);
    ObjectSetInteger(0, headerName, OBJPROP_BGCOLOR, C'0,120,215');
    ObjectSetInteger(0, headerName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, headerName, OBJPROP_BACK, false);
    ObjectSetInteger(0, headerName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, headerName, OBJPROP_ZORDER, 1001);
    
    CreatePanelButtons();
}

//+------------------------------------------------------------------+
//| Panel butonlarını oluştur                                       |
//+------------------------------------------------------------------+
void CreatePanelButtons()
{
    int currentX = StartX;
    int currentY = StartY;
    
    int btn_y = currentY + 120;
    int btn_width = (PANEL_WIDTH - 4 * MARGIN) / 3;
    
    // Karlı Kapat butonu
    CreateButton("BtnProfit", "💚 Karlı Kapat", currentX + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'0,128,0');
    
    // Zarar Kapat butonu
    CreateButton("BtnLoss", "❤️ Zarar Kapat", currentX + MARGIN + btn_width + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'128,0,0');
    
    // Hepsini Kapat butonu
    CreateButton("BtnAll", "🚫 Hepsini Kapat", currentX + MARGIN, btn_y + BUTTON_HEIGHT + MARGIN, 2 * btn_width + MARGIN, BUTTON_HEIGHT, C'128,128,0');
}

//+------------------------------------------------------------------+
//| Buton oluştur                                                   |
//+------------------------------------------------------------------+
void CreateButton(string name, string text, int x, int y, int width, int height, color bgColor)
{
    string objName = g_panelName + "_" + name;
    
    // Buton arka planı
    ObjectCreate(0, objName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, objName, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, objName, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, objName, OBJPROP_BGCOLOR, bgColor);
    ObjectSetInteger(0, objName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, C'70,70,70');
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1002);
    
    // Buton metni
    ObjectCreate(0, objName + "_Text", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_XDISTANCE, x + width/2);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_YDISTANCE, y + height/2 - 6);
    ObjectSetString(0, objName + "_Text", OBJPROP_TEXT, text);
    ObjectSetString(0, objName + "_Text", OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, objName + "_Text", OBJPROP_FONTSIZE, 8);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_ANCHOR, ANCHOR_CENTER);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_BACK, false);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_ZORDER, 1003);
}

//+------------------------------------------------------------------+
//| Sürükleme sonrası güncelle                                      |
//+------------------------------------------------------------------+
void UpdateAfterDrag()
{
    // Yeni pozisyonu al
    int newX = (int)ObjectGetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE);
    int newY = (int)ObjectGetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE);
    
    // Tüm panel elementlerini sil ve yeniden oluştur
    ObjectsDeleteAll(0, g_panelName);
    
    // Yeni pozisyonda panel oluştur
    CreatePanelAtPosition(newX, newY);
    UpdatePanelData();
}

//+------------------------------------------------------------------+
//| Belirli pozisyonda panel oluştur                                |
//+------------------------------------------------------------------+
void CreatePanelAtPosition(int x, int y)
{
    // Ana panel arka planı
    string bgName = g_panelName + "_Background";
    ObjectCreate(0, bgName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, bgName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, bgName, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, bgName, OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, bgName, OBJPROP_YSIZE, PANEL_HEIGHT);
    ObjectSetInteger(0, bgName, OBJPROP_BGCOLOR, C'30,30,30');
    ObjectSetInteger(0, bgName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, bgName, OBJPROP_COLOR, C'100,100,100');
    ObjectSetInteger(0, bgName, OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, bgName, OBJPROP_BACK, false);
    ObjectSetInteger(0, bgName, OBJPROP_SELECTABLE, true);
    ObjectSetInteger(0, bgName, OBJPROP_ZORDER, 1000);
    
    // Header
    string headerName = g_panelName + "_Header";
    ObjectCreate(0, headerName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, headerName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, headerName, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, headerName, OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, headerName, OBJPROP_YSIZE, 25);
    ObjectSetInteger(0, headerName, OBJPROP_BGCOLOR, C'0,120,215');
    ObjectSetInteger(0, headerName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, headerName, OBJPROP_BACK, false);
    ObjectSetInteger(0, headerName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, headerName, OBJPROP_ZORDER, 1001);
    
    CreateButtonsAtPosition(x, y);
}

//+------------------------------------------------------------------+
//| Belirli pozisyonda butonları oluştur                            |
//+------------------------------------------------------------------+
void CreateButtonsAtPosition(int panelX, int panelY)
{
    int btn_y = panelY + 120;
    int btn_width = (PANEL_WIDTH - 4 * MARGIN) / 3;
    
    // Karlı Kapat butonu
    CreateButton("BtnProfit", "💚 Karlı Kapat", panelX + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'0,128,0');
    
    // Zarar Kapat butonu
    CreateButton("BtnLoss", "❤️ Zarar Kapat", panelX + MARGIN + btn_width + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'128,0,0');
    
    // Hepsini Kapat butonu
    CreateButton("BtnAll", "🚫 Hepsini Kapat", panelX + MARGIN, btn_y + BUTTON_HEIGHT + MARGIN, 2 * btn_width + MARGIN, BUTTON_HEIGHT, C'128,128,0');
}

//+------------------------------------------------------------------+
//| Panel verilerini güncelle                                       |
//+------------------------------------------------------------------+
void UpdatePanelData()
{
    // Mevcut panel pozisyonunu al
    int currentX = (int)ObjectGetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE);
    int currentY = (int)ObjectGetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE);
    
    if(currentX == 0 && currentY == 0) {
        currentX = StartX;
        currentY = StartY;
    }
    
    // Panel verilerini hesapla
    int positionCount = 0;
    double totalVolume = 0;
    double totalProfit = 0;
    int profitableCount = 0;
    int losingCount = 0;
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;
            
        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);
        
        positionCount++;
        totalVolume += volume;
        totalProfit += profit;
        
        if(profit > 0)
            profitableCount++;
        else if(profit < 0)
            losingCount++;
    }
    
    // Günlük P&L hesapla
    double dailyPL = CalculateDailyPL(totalProfit);
    
    // Panel içeriğini güncelle
    DrawPanelContent(currentX, currentY, positionCount, totalVolume, totalProfit, profitableCount, losingCount, dailyPL);
}

//+------------------------------------------------------------------+
//| Panel içeriğini çiz                                             |
//+------------------------------------------------------------------+
void DrawPanelContent(int panelX, int panelY, int posCount, double totalVol, double totalPL, int profitCount, int lossCount, double dailyPL)
{
    int y_pos = panelY + 30;

    // Başlık
    CreateLabel("Title", "📊 İşlem Yönetim Paneli", panelX + PANEL_WIDTH/2, panelY + 12, clrWhite, 10, ANCHOR_CENTER);

    // Günlük P&L
    string dailyText = "💰 Günlük P&L: $" + DoubleToString(dailyPL, 2);
    color dailyColor = (dailyPL >= 0) ? clrLimeGreen : clrTomato;
    CreateLabel("DailyPL", dailyText, panelX + MARGIN, y_pos, dailyColor, 9);
    y_pos += 18;

    // Açık pozisyon sayısı
    string posText = "📈 Açık Pozisyon: " + IntegerToString(posCount);
    CreateLabel("PositionCount", posText, panelX + MARGIN, y_pos, clrWhite, 8);
    y_pos += 16;

    // Toplam hacim
    string volText = "📊 Toplam Hacim: " + DoubleToString(totalVol, 2);
    CreateLabel("TotalVolume", volText, panelX + MARGIN, y_pos, clrWhite, 8);
    y_pos += 16;

    // Toplam P&L
    string plText = "💵 Toplam P&L: $" + DoubleToString(totalPL, 2);
    color plColor = (totalPL >= 0) ? clrLimeGreen : clrTomato;
    CreateLabel("TotalPL", plText, panelX + MARGIN, y_pos, plColor, 9);
    y_pos += 16;

    // Karlı/Zararlı sayısı
    string countText = "📊 Karlı: " + IntegerToString(profitCount) + " | Zararlı: " + IntegerToString(lossCount);
    CreateLabel("ProfitLossCount", countText, panelX + MARGIN, y_pos, clrWhite, 8);
}

//+------------------------------------------------------------------+
//| Label oluştur                                                   |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color clr, int fontSize, ENUM_ANCHOR_POINT anchor = ANCHOR_LEFT_UPPER)
{
    string objName = g_panelName + "_" + name;

    ObjectDelete(0, objName);
    ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetString(0, objName, OBJPROP_TEXT, text);
    ObjectSetString(0, objName, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, fontSize);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, objName, OBJPROP_ANCHOR, anchor);
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1002);
}

//+------------------------------------------------------------------+
//| Günlük P&L hesapla                                             |
//+------------------------------------------------------------------+
double CalculateDailyPL(double currentPL)
{
    double dailyPL = 0;
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    HistorySelect(todayStart, TimeCurrent());

    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
            if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);

                dailyPL += (profit + commission + swap);
            }
        }
    }

    dailyPL += currentPL;
    return dailyPL;
}

//+------------------------------------------------------------------+
//| Karlı pozisyonları kapat                                        |
//+------------------------------------------------------------------+
void CloseProfitablePositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("💚 Karlı pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double profit = PositionGetDouble(POSITION_PROFIT);
        if(profit > 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closedCount++;
                Print("✅ Karlı pozisyon kapatıldı: ", ticket, " Kar: $", DoubleToString(profit, 2));
            }
        }
    }

    Print("💚 Toplam ", closedCount, " karlı pozisyon kapatıldı");
    UpdatePanelData();
}

//+------------------------------------------------------------------+
//| Zararlı pozisyonları kapat                                      |
//+------------------------------------------------------------------+
void CloseLosingPositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("❤️ Zararlı pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double profit = PositionGetDouble(POSITION_PROFIT);
        if(profit < 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closedCount++;
                Print("✅ Zararlı pozisyon kapatıldı: ", ticket, " Zarar: $", DoubleToString(profit, 2));
            }
        }
    }

    Print("❤️ Toplam ", closedCount, " zararlı pozisyon kapatıldı");
    UpdatePanelData();
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("🚫 Tüm pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        double profit = PositionGetDouble(POSITION_PROFIT);

        if(trade.PositionClose(ticket))
        {
            closedCount++;
            Print("✅ Pozisyon kapatıldı: ", ticket, " P&L: $", DoubleToString(profit, 2));
        }
    }

    Print("🚫 Toplam ", closedCount, " pozisyon kapatıldı");
    UpdatePanelData();
}
