//+------------------------------------------------------------------+
//|                                        SuperArrow_Simple_MT5.mq5 |
//|                        Copyright 2025, Emre USUN - Horizon Team |
//|                      Super Arrow Indicator - Basit Test Versiyonu |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Emre USUN - Horizon Team"
#property link      "https://github.com/emreusun87"
#property version   "1.0"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- Plot ayarları
#property indicator_label1  "BUY"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  3

#property indicator_label2  "SELL"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  3

//--- Input parameters
input int FasterMA = 5;         // Hızlı MA Periyodu
input int SlowerMA = 12;        // Yavaş MA Periyodu
input int RSIPeriod = 14;       // RSI Periyodu
input int ArrowDistance = 100;  // Arrow mesafesi (point)

//--- Indicator buffers
double BuyArrowBuffer[];
double SellArrowBuffer[];

//--- Global variables
int fastMA_handle;
int slowMA_handle;
int rsi_handle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Buffer ayarları
    SetIndexBuffer(0, BuyArrowBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SellArrowBuffer, INDICATOR_DATA);
    
    // Arrow kodları
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // BUY arrow (yukarı)
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // SELL arrow (aşağı)
    
    // Buffer'ları başlat
    ArraySetAsSeries(BuyArrowBuffer, true);
    ArraySetAsSeries(SellArrowBuffer, true);
    
    // Handle'ları oluştur
    fastMA_handle = iMA(_Symbol, _Period, FasterMA, 0, MODE_EMA, PRICE_CLOSE);
    slowMA_handle = iMA(_Symbol, _Period, SlowerMA, 0, MODE_EMA, PRICE_CLOSE);
    rsi_handle = iRSI(_Symbol, _Period, RSIPeriod, PRICE_CLOSE);
    
    // Handle kontrolü
    if(fastMA_handle == INVALID_HANDLE || slowMA_handle == INVALID_HANDLE || rsi_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle'ları oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Indicator ismi
    IndicatorSetString(INDICATOR_SHORTNAME, "SuperArrow Simple(" + IntegerToString(FasterMA) + "/" + IntegerToString(SlowerMA) + ")");
    
    Print("📊 Super Arrow Simple MT5 başlatıldı");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // Yeterli veri var mı kontrol et
    if(rates_total < SlowerMA + 10) return 0;
    
    // Handle'lar hazır mı kontrol et
    if(BarsCalculated(fastMA_handle) < rates_total || 
       BarsCalculated(slowMA_handle) < rates_total ||
       BarsCalculated(rsi_handle) < rates_total)
        return 0;
    
    // Buffer'ları hazırla
    double fastMA[], slowMA[], rsi_values[];
    
    ArraySetAsSeries(fastMA, true);
    ArraySetAsSeries(slowMA, true);
    ArraySetAsSeries(rsi_values, true);
    
    // Veri kopyala
    int copy_bars = rates_total - prev_calculated + 10;
    if(prev_calculated == 0) copy_bars = rates_total;
    
    if(CopyBuffer(fastMA_handle, 0, 0, copy_bars, fastMA) <= 0) return 0;
    if(CopyBuffer(slowMA_handle, 0, 0, copy_bars, slowMA) <= 0) return 0;
    if(CopyBuffer(rsi_handle, 0, 0, copy_bars, rsi_values) <= 0) return 0;
    
    // Array'leri time series olarak ayarla
    ArraySetAsSeries(time, true);
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    // Hesaplama başlangıç noktası
    int start = prev_calculated;
    if(start == 0) start = SlowerMA + 10;
    
    // Ana hesaplama döngüsü
    for(int i = start; i < rates_total - 1; i++)
    {
        // Buffer index'leri (time series)
        int current_index = rates_total - 1 - i;
        int prev_index = current_index + 1;
        
        // Buffer'ları temizle
        BuyArrowBuffer[current_index] = EMPTY_VALUE;
        SellArrowBuffer[current_index] = EMPTY_VALUE;
        
        // Basit sinyal koşulları
        // BUY: Hızlı MA yukarı kestiğinde ve RSI > 50
        bool ma_cross_up = (fastMA[current_index] > slowMA[current_index]) && 
                          (fastMA[prev_index] <= slowMA[prev_index]);
        bool rsi_bullish = rsi_values[current_index] > 50.0;
        
        // SELL: Hızlı MA aşağı kestiğinde ve RSI < 50
        bool ma_cross_down = (fastMA[current_index] < slowMA[current_index]) && 
                            (fastMA[prev_index] >= slowMA[prev_index]);
        bool rsi_bearish = rsi_values[current_index] < 50.0;
        
        // BUY Sinyali
        if(ma_cross_up && rsi_bullish)
        {
            BuyArrowBuffer[current_index] = low[current_index] - (ArrowDistance * _Point);
            Print("🟢 BUY Signal - Bar: ", i, " Time: ", TimeToString(time[current_index]), 
                  " Price: ", DoubleToString(close[current_index], _Digits));
        }
        
        // SELL Sinyali
        if(ma_cross_down && rsi_bearish)
        {
            SellArrowBuffer[current_index] = high[current_index] + (ArrowDistance * _Point);
            Print("🔴 SELL Signal - Bar: ", i, " Time: ", TimeToString(time[current_index]), 
                  " Price: ", DoubleToString(close[current_index], _Digits));
        }
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| Indicator temizleme                                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Handle'ları serbest bırak
    if(fastMA_handle != INVALID_HANDLE) IndicatorRelease(fastMA_handle);
    if(slowMA_handle != INVALID_HANDLE) IndicatorRelease(slowMA_handle);
    if(rsi_handle != INVALID_HANDLE) IndicatorRelease(rsi_handle);
    
    Print("📊 Super Arrow Simple MT5 durduruldu");
}
