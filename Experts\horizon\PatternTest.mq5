//+------------------------------------------------------------------+
//| Pattern Test EA - XAUUSD Pattern Fonksiyonlarını Test Et        |
//+------------------------------------------------------------------+
#property copyright "Pattern Test"
#property version   "1.00"
#property strict

#include "XAUUSD_Patterns.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("🧪 Pattern Test EA başlatıldı");
    
    // Pattern fonksiyonlarını test et
    TestPatternFunctions();
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("🧪 Pattern Test EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Her 100 tick'te bir pattern kontrol et
    static int tick_count = 0;
    tick_count++;
    
    if(tick_count >= 100)
    {
        tick_count = 0;
        CheckCurrentPatterns();
    }
}

//+------------------------------------------------------------------+
//| Pattern fonksiyonlarını test et                                 |
//+------------------------------------------------------------------+
void TestPatternFunctions()
{
    Print("🔍 Pattern fonksiyonları test ediliyor...");
    
    // Yardımcı fonksiyonları test et
    double bar_size = GetBarSize(1);
    double body_size = GetBodySize(1);
    double close_pos = GetClosePosition(1);
    
    Print("📊 Bar 1 Metrikleri:");
    Print("   Bar Size: ", DoubleToString(bar_size, 1), " pip");
    Print("   Body Size: ", DoubleToString(body_size, 1), " pip");
    Print("   Close Position: ", DoubleToString(close_pos, 1), "%");
    Print("   Is Bullish: ", IsBullish(1));
    Print("   Is Bearish: ", IsBearish(1));
    Print("   Is Doji: ", IsDoji(1));
    
    // Pattern kontrolleri
    Print("\n🎯 Pattern Kontrolleri:");
    
    if(Check_BUY_Reversal_After_2Bearish(1))
        Print("✅ BUY_Reversal_After_2Bearish pattern tespit edildi!");
    
    if(Check_BUY_London_Momentum(1))
        Print("✅ BUY_London_Momentum pattern tespit edildi!");
    
    if(Check_BUY_Doji_After_StrongDown(1))
        Print("✅ BUY_Doji_After_StrongDown pattern tespit edildi!");
    
    if(Check_SELL_NY_Momentum(1))
        Print("✅ SELL_NY_Momentum pattern tespit edildi!");
    
    if(Check_BUY_Hammer_Pattern(1))
        Print("✅ BUY_Hammer_Pattern pattern tespit edildi!");
    
    // Ana pattern kontrol fonksiyonu
    string pattern_result = CheckAllPatterns(1);
    Print("🎯 Ana Pattern Sonucu: ", pattern_result);
    
    Print("✅ Pattern test tamamlandı!");
}

//+------------------------------------------------------------------+
//| Mevcut pattern'leri kontrol et                                  |
//+------------------------------------------------------------------+
void CheckCurrentPatterns()
{
    string pattern = CheckAllPatterns(1);
    
    if(pattern != "NONE")
    {
        Print("🚨 PATTERN SİNYALİ: ", pattern);
        Print("   Zaman: ", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
        Print("   Fiyat: ", DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
        
        // Pattern detayları
        Print("📊 Bar Detayları:");
        Print("   Bar Size: ", DoubleToString(GetBarSize(1), 1), " pip");
        Print("   Body Size: ", DoubleToString(GetBodySize(1), 1), " pip");
        Print("   Close Position: ", DoubleToString(GetClosePosition(1), 1), "%");
        Print("   Upper Shadow: ", DoubleToString(GetUpperShadow(1), 1), " pip");
        Print("   Lower Shadow: ", DoubleToString(GetLowerShadow(1), 1), " pip");
    }
}
