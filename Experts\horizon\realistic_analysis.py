#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XAUUSD M5 Gerçekçi Karar Matrisi Analizi
Gerçek piyasa koşullarında kullanılabilir pattern'ler
"""

import pandas as pd
import numpy as np

def load_data():
    """<PERSON>eriyi yükle"""
    df = pd.read_csv('XAUUSD_M5_202501020100_202507170555.csv', sep='\t')
    df.columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'TickVol', 'Vol', 'Spread']
    df = df.drop(0).reset_index(drop=True)
    
    for col in ['Open', 'High', 'Low', 'Close', 'TickVol', 'Spread']:
        df[col] = pd.to_numeric(df[col])
    
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
    return df

def calculate_realistic_metrics(df):
    """Gerçekçi metrikler hesapla"""
    print("📊 Gerçekçi metrikler hesaplanıyor...")
    
    # Temel metrikler
    df['BarSize'] = (df['High'] - df['Low']) * 10  # pip
    df['BodySize'] = abs(df['Close'] - df['Open']) * 10
    df['ClosePosition'] = (df['Close'] - df['Low']) / (df['High'] - df['Low']) * 100
    df['ClosePosition'] = df['ClosePosition'].fillna(50)
    
    # Bar türü
    df['BarType'] = np.where(df['Close'] > df['Open'], 'BULLISH',
                    np.where(df['Close'] < df['Open'], 'BEARISH', 'DOJI'))
    
    # Sonraki bar hareketi (gerçekçi hedefler)
    df['NextMove'] = (df['Close'].shift(-1) - df['Close']) * 10
    df['NextDirection'] = np.where(df['NextMove'] > 1, 'UP',
                          np.where(df['NextMove'] < -1, 'DOWN', 'SIDEWAYS'))
    
    # Ardışık bar sayısı
    df['ConsecutiveBars'] = 1
    for i in range(1, len(df)):
        if df.loc[i, 'BarType'] == df.loc[i-1, 'BarType'] and df.loc[i-1, 'BarType'] != 'DOJI':
            df.loc[i, 'ConsecutiveBars'] = df.loc[i-1, 'ConsecutiveBars'] + 1
    
    # Volatilite
    df['ATR5'] = df['BarSize'].rolling(5).mean()
    df['VolCategory'] = pd.cut(df['BarSize'], 
                              bins=[0, 5, 12, 20, 100], 
                              labels=['VeryLow', 'Low', 'Medium', 'High'])
    
    # Saat kategorisi
    df['Hour'] = df['DateTime'].dt.hour
    df['Session'] = 'QUIET'
    df.loc[df['Hour'].between(8, 11), 'Session'] = 'LONDON_OPEN'
    df.loc[df['Hour'].between(14, 17), 'Session'] = 'NY_OPEN'
    df.loc[df['Hour'].between(15, 16), 'Session'] = 'OVERLAP'
    
    print("✅ Metrikler hazırlandı")
    return df

def find_realistic_patterns(df):
    """Gerçekçi pattern'ler bul"""
    print("🔍 Gerçekçi pattern'ler aranıyor...")
    
    patterns = []
    
    # Pattern 1: Reversal After 2+ Consecutive Bars
    print("\n📈 Reversal Pattern Analizi:")
    for consecutive in [2, 3]:
        for bar_type in ['BULLISH', 'BEARISH']:
            target_direction = 'DOWN' if bar_type == 'BULLISH' else 'UP'
            
            condition = (
                (df['ConsecutiveBars'] >= consecutive) &
                (df['BarType'] == bar_type) &
                (df['BarSize'] >= 5) &
                (df['BarSize'] <= 25)
            )
            
            if condition.sum() > 30:  # En az 30 test
                success = (df[condition]['NextDirection'] == target_direction).sum()
                total = condition.sum()
                success_rate = (success / total) * 100
                avg_move = df[condition]['NextMove'].mean()
                
                pattern_name = f"Reversal_{consecutive}_{bar_type}"
                print(f"   {pattern_name}: {success_rate:.1f}% ({success}/{total})")
                
                patterns.append({
                    'name': pattern_name,
                    'type': 'REVERSAL',
                    'success_rate': success_rate,
                    'avg_move': avg_move,
                    'samples': total,
                    'target': target_direction,
                    'conditions': {
                        'consecutive_bars': consecutive,
                        'bar_type': bar_type,
                        'min_bar_size': 5,
                        'max_bar_size': 25
                    }
                })
    
    # Pattern 2: Breakout After Low Volatility
    print("\n🚀 Breakout Pattern Analizi:")
    low_vol_condition = df['BarSize'] < df['ATR5'] * 0.8
    
    for direction in ['UP', 'DOWN']:
        if direction == 'UP':
            breakout_condition = (
                low_vol_condition &
                (df['BarType'] == 'BULLISH') &
                (df['BarSize'] > df['ATR5'] * 1.2) &
                (df['ClosePosition'] > 70)
            )
        else:
            breakout_condition = (
                low_vol_condition &
                (df['BarType'] == 'BEARISH') &
                (df['BarSize'] > df['ATR5'] * 1.2) &
                (df['ClosePosition'] < 30)
            )
        
        if breakout_condition.sum() > 20:
            success = (df[breakout_condition]['NextDirection'] == direction).sum()
            total = breakout_condition.sum()
            success_rate = (success / total) * 100
            avg_move = df[breakout_condition]['NextMove'].mean()
            
            pattern_name = f"Breakout_{direction}"
            print(f"   {pattern_name}: {success_rate:.1f}% ({success}/{total})")
            
            patterns.append({
                'name': pattern_name,
                'type': 'BREAKOUT',
                'success_rate': success_rate,
                'avg_move': avg_move,
                'samples': total,
                'target': direction,
                'conditions': {
                    'low_volatility': True,
                    'bar_type': 'BULLISH' if direction == 'UP' else 'BEARISH',
                    'breakout_size': 1.2,
                    'close_position': '>70%' if direction == 'UP' else '<30%'
                }
            })
    
    # Pattern 3: Session-Based Momentum
    print("\n🕐 Session Pattern Analizi:")
    for session in ['LONDON_OPEN', 'NY_OPEN', 'OVERLAP']:
        session_data = df[df['Session'] == session]
        
        for direction in ['UP', 'DOWN']:
            if direction == 'UP':
                momentum_condition = (
                    (session_data['BarType'] == 'BULLISH') &
                    (session_data['BodySize'] > session_data['BarSize'] * 0.6) &
                    (session_data['BarSize'] > 8)
                )
            else:
                momentum_condition = (
                    (session_data['BarType'] == 'BEARISH') &
                    (session_data['BodySize'] > session_data['BarSize'] * 0.6) &
                    (session_data['BarSize'] > 8)
                )
            
            if momentum_condition.sum() > 20:
                success = (session_data[momentum_condition]['NextDirection'] == direction).sum()
                total = momentum_condition.sum()
                success_rate = (success / total) * 100
                avg_move = session_data[momentum_condition]['NextMove'].mean()
                
                pattern_name = f"{session}_Momentum_{direction}"
                print(f"   {pattern_name}: {success_rate:.1f}% ({success}/{total})")
                
                patterns.append({
                    'name': pattern_name,
                    'type': 'MOMENTUM',
                    'success_rate': success_rate,
                    'avg_move': avg_move,
                    'samples': total,
                    'target': direction,
                    'conditions': {
                        'session': session,
                        'bar_type': 'BULLISH' if direction == 'UP' else 'BEARISH',
                        'body_ratio': 0.6,
                        'min_bar_size': 8
                    }
                })
    
    # Pattern 4: Doji Reversal
    print("\n🎯 Doji Pattern Analizi:")
    doji_condition = (
        (df['BodySize'] < 2) &
        (df['BarSize'] > 8) &
        (df['BarSize'] < 20)
    )
    
    for direction in ['UP', 'DOWN']:
        if direction == 'UP':
            context_condition = doji_condition & (df['ClosePosition'] < 40)
        else:
            context_condition = doji_condition & (df['ClosePosition'] > 60)
        
        if context_condition.sum() > 15:
            success = (df[context_condition]['NextDirection'] == direction).sum()
            total = context_condition.sum()
            success_rate = (success / total) * 100
            avg_move = df[context_condition]['NextMove'].mean()
            
            pattern_name = f"Doji_Reversal_{direction}"
            print(f"   {pattern_name}: {success_rate:.1f}% ({success}/{total})")
            
            patterns.append({
                'name': pattern_name,
                'type': 'DOJI',
                'success_rate': success_rate,
                'avg_move': avg_move,
                'samples': total,
                'target': direction,
                'conditions': {
                    'body_size': '<2 pip',
                    'bar_size': '8-20 pip',
                    'close_position': '<40%' if direction == 'UP' else '>60%'
                }
            })
    
    return sorted(patterns, key=lambda x: x['success_rate'], reverse=True)

def generate_trading_rules(patterns):
    """Trading kuralları oluştur"""
    print("\n📋 TRADING KURALLARI:")
    print("="*60)
    
    best_patterns = [p for p in patterns if p['success_rate'] > 52 and p['samples'] > 30]
    
    for i, pattern in enumerate(best_patterns[:5], 1):
        print(f"\n{i}. 🎯 {pattern['name']}")
        print(f"   📊 Başarı: {pattern['success_rate']:.1f}% ({pattern['samples']} test)")
        print(f"   📈 Ortalama: {pattern['avg_move']:.1f} pip")
        print(f"   🎯 Hedef: {pattern['target']}")
        print(f"   📋 Koşullar:")
        
        for key, value in pattern['conditions'].items():
            print(f"      • {key}: {value}")
        
        # MQL5 kodu önerisi
        signal_type = "BUY" if pattern['target'] == 'UP' else "SELL"
        print(f"   💻 MQL5: Check{pattern['name'].replace(' ', '_')}() → {signal_type} Signal")

def main():
    """Ana analiz"""
    print("🚀 XAUUSD M5 Gerçekçi Pattern Analizi")
    print("="*50)
    
    df = load_data()
    print(f"📊 {len(df)} bar yüklendi")
    
    df = calculate_realistic_metrics(df)
    patterns = find_realistic_patterns(df)
    
    print(f"\n🏆 {len(patterns)} pattern bulundu")
    generate_trading_rules(patterns)
    
    # Özet istatistikler
    print(f"\n📈 ÖZET İSTATİSTİKLER:")
    print(f"   • Toplam analiz: {len(df)} bar")
    print(f"   • Tarih aralığı: {df['DateTime'].min().date()} - {df['DateTime'].max().date()}")
    print(f"   • Ortalama bar büyüklüğü: {df['BarSize'].mean():.1f} pip")
    print(f"   • Ortalama volatilite: {df['ATR5'].mean():.1f} pip")
    
    direction_counts = df['NextDirection'].value_counts()
    print(f"   • Yön dağılımı: UP {direction_counts.get('UP', 0)} | DOWN {direction_counts.get('DOWN', 0)} | SIDEWAYS {direction_counts.get('SIDEWAYS', 0)}")

if __name__ == "__main__":
    main()
