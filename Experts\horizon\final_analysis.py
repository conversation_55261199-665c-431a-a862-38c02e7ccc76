#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XAUUSD M5 Final Karar Matrisi
En kullanışlı pattern'ler ve MQL5 şablonları
"""

import pandas as pd
import numpy as np

def load_and_analyze():
    """Veriyi yükle ve analiz et"""
    print("🎯 XAUUSD M5 Final Analiz")
    print("="*40)
    
    df = pd.read_csv('XAUUSD_M5_202501020100_202507170555.csv', sep='\t')
    df.columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'TickVol', 'Vol', 'Spread']
    df = df.drop(0).reset_index(drop=True)
    
    for col in ['Open', 'High', 'Low', 'Close', 'TickVol', 'Spread']:
        df[col] = pd.to_numeric(df[col])
    
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
    
    # Kritik metrikler
    df['BarSize'] = (df['High'] - df['Low']) * 10  # pip
    df['BodySize'] = abs(df['Close'] - df['Open']) * 10
    df['UpperShadow'] = (df['High'] - df[['Open', 'Close']].max(axis=1)) * 10
    df['LowerShadow'] = (df[['Open', 'Close']].min(axis=1) - df['Low']) * 10
    df['ClosePosition'] = (df['Close'] - df['Low']) / (df['High'] - df['Low']) * 100
    df['ClosePosition'] = df['ClosePosition'].fillna(50)
    
    # Bar türü ve yön
    df['IsBullish'] = df['Close'] > df['Open']
    df['IsBearish'] = df['Close'] < df['Open']
    df['IsDoji'] = abs(df['Close'] - df['Open']) * 10 < 2  # Body < 2 pip
    
    # Hedef (sonraki bar)
    df['NextMove'] = (df['Close'].shift(-1) - df['Close']) * 10
    df['NextUp'] = df['NextMove'] > 1
    df['NextDown'] = df['NextMove'] < -1
    
    # Volatilite
    df['ATR5'] = df['BarSize'].rolling(5).mean()
    df['IsLowVol'] = df['BarSize'] < df['ATR5'] * 0.8
    df['IsHighVol'] = df['BarSize'] > df['ATR5'] * 1.3
    
    # Saat
    df['Hour'] = df['DateTime'].dt.hour
    df['IsLondonOpen'] = df['Hour'].between(8, 11)
    df['IsNYOpen'] = df['Hour'].between(14, 17)
    df['IsOverlap'] = df['Hour'].between(15, 16)
    
    print(f"📊 {len(df)} bar analiz edildi")
    return df

def find_best_combinations(df):
    """En iyi kombinasyonları bul"""
    print("\n🔍 En İyi Kombinasyonlar:")
    print("-" * 40)
    
    results = []
    
    # 1. Reversal After Consecutive Bearish + Low Close
    condition = (
        (df['IsBearish']) &
        (df['IsBearish'].shift(1)) &  # Önceki bar da bearish
        (df['ClosePosition'] < 30) &  # Alt %30'da kapanış
        (df['BarSize'] > 8) &         # En az 8 pip
        (df['BarSize'] < 30)          # Max 30 pip
    )
    
    success_rate = (df[condition]['NextUp']).mean() * 100
    avg_move = df[condition]['NextMove'].mean()
    sample_size = condition.sum()
    
    results.append({
        'name': 'BUY_Reversal_After_2Bearish',
        'success_rate': success_rate,
        'avg_move': avg_move,
        'samples': sample_size,
        'description': '2 ardışık düşüş + alt kapanış → BUY'
    })
    
    # 2. Reversal After Consecutive Bullish + High Close
    condition = (
        (df['IsBullish']) &
        (df['IsBullish'].shift(1)) &  # Önceki bar da bullish
        (df['ClosePosition'] > 70) &  # Üst %30'da kapanış
        (df['BarSize'] > 8) &         # En az 8 pip
        (df['BarSize'] < 30)          # Max 30 pip
    )
    
    success_rate = (df[condition]['NextDown']).mean() * 100
    avg_move = df[condition]['NextMove'].mean()
    sample_size = condition.sum()
    
    results.append({
        'name': 'SELL_Reversal_After_2Bullish',
        'success_rate': success_rate,
        'avg_move': avg_move,
        'samples': sample_size,
        'description': '2 ardışık yükseliş + üst kapanış → SELL'
    })
    
    # 3. Doji After Strong Move
    condition = (
        (df['IsDoji']) &
        (df['BarSize'] > 10) &        # Büyük doji
        (df['BarSize'].shift(1) > 15) & # Önceki bar güçlü
        (df['IsBearish'].shift(1))    # Önceki bar bearish
    )
    
    success_rate = (df[condition]['NextUp']).mean() * 100
    avg_move = df[condition]['NextMove'].mean()
    sample_size = condition.sum()
    
    results.append({
        'name': 'BUY_Doji_After_StrongDown',
        'success_rate': success_rate,
        'avg_move': avg_move,
        'samples': sample_size,
        'description': 'Güçlü düşüş sonrası büyük doji → BUY'
    })
    
    # 4. Hammer Pattern (Long Lower Shadow)
    condition = (
        (df['LowerShadow'] > df['BodySize'] * 2) &  # Alt gölge > body * 2
        (df['LowerShadow'] > 5) &                   # En az 5 pip gölge
        (df['ClosePosition'] > 60) &                # Üst bölgede kapanış
        (df['BarSize'] > 8)                         # Minimum bar büyüklüğü
    )
    
    success_rate = (df[condition]['NextUp']).mean() * 100
    avg_move = df[condition]['NextMove'].mean()
    sample_size = condition.sum()
    
    results.append({
        'name': 'BUY_Hammer_Pattern',
        'success_rate': success_rate,
        'avg_move': avg_move,
        'samples': sample_size,
        'description': 'Hammer pattern (uzun alt gölge) → BUY'
    })
    
    # 5. Shooting Star Pattern (Long Upper Shadow)
    condition = (
        (df['UpperShadow'] > df['BodySize'] * 2) &  # Üst gölge > body * 2
        (df['UpperShadow'] > 5) &                   # En az 5 pip gölge
        (df['ClosePosition'] < 40) &                # Alt bölgede kapanış
        (df['BarSize'] > 8)                         # Minimum bar büyüklüğü
    )
    
    success_rate = (df[condition]['NextDown']).mean() * 100
    avg_move = df[condition]['NextMove'].mean()
    sample_size = condition.sum()
    
    results.append({
        'name': 'SELL_ShootingStar_Pattern',
        'success_rate': success_rate,
        'avg_move': avg_move,
        'samples': sample_size,
        'description': 'Shooting Star pattern (uzun üst gölge) → SELL'
    })
    
    # 6. London Open Momentum
    condition = (
        (df['IsLondonOpen']) &
        (df['IsBullish']) &
        (df['BodySize'] > df['BarSize'] * 0.6) &    # Güçlü body
        (df['BarSize'] > 12)                        # Büyük bar
    )
    
    success_rate = (df[condition]['NextUp']).mean() * 100
    avg_move = df[condition]['NextMove'].mean()
    sample_size = condition.sum()
    
    results.append({
        'name': 'BUY_London_Momentum',
        'success_rate': success_rate,
        'avg_move': avg_move,
        'samples': sample_size,
        'description': 'London açılış momentum (güçlü bullish) → BUY'
    })
    
    # 7. NY Open Momentum
    condition = (
        (df['IsNYOpen']) &
        (df['IsBearish']) &
        (df['BodySize'] > df['BarSize'] * 0.6) &    # Güçlü body
        (df['BarSize'] > 12)                        # Büyük bar
    )
    
    success_rate = (df[condition]['NextDown']).mean() * 100
    avg_move = df[condition]['NextMove'].mean()
    sample_size = condition.sum()
    
    results.append({
        'name': 'SELL_NY_Momentum',
        'success_rate': success_rate,
        'avg_move': avg_move,
        'samples': sample_size,
        'description': 'NY açılış momentum (güçlü bearish) → SELL'
    })
    
    # 8. Breakout After Squeeze
    condition = (
        (df['IsLowVol']) &                          # Düşük volatilite
        (df['BarSize'] > df['ATR5'] * 1.5) &       # Breakout bar
        (df['IsBullish']) &                         # Bullish breakout
        (df['BodySize'] > df['BarSize'] * 0.7)     # Güçlü body
    )
    
    success_rate = (df[condition]['NextUp']).mean() * 100
    avg_move = df[condition]['NextMove'].mean()
    sample_size = condition.sum()
    
    results.append({
        'name': 'BUY_Breakout_After_Squeeze',
        'success_rate': success_rate,
        'avg_move': avg_move,
        'samples': sample_size,
        'description': 'Düşük volatilite sonrası bullish breakout → BUY'
    })
    
    return sorted(results, key=lambda x: x['success_rate'], reverse=True)

def generate_mql5_templates(results):
    """MQL5 şablonları oluştur"""
    print("\n💻 MQL5 ŞABLONLARI:")
    print("=" * 50)
    
    mql5_code = """
//+------------------------------------------------------------------+
//| XAUUSD M5 Pattern-Based Trading Functions                       |
//| Gerçek verilerden çıkarılan pattern'ler                         |
//+------------------------------------------------------------------+

// Yardımcı fonksiyonlar
double GetBarSize(int bar) { return (iHigh(_Symbol, _Period, bar) - iLow(_Symbol, _Period, bar)) / _Point; }
double GetBodySize(int bar) { return MathAbs(iClose(_Symbol, _Period, bar) - iOpen(_Symbol, _Period, bar)) / _Point; }
double GetUpperShadow(int bar) { 
    double high = iHigh(_Symbol, _Period, bar);
    double open = iOpen(_Symbol, _Period, bar);
    double close = iClose(_Symbol, _Period, bar);
    return (high - MathMax(open, close)) / _Point;
}
double GetLowerShadow(int bar) {
    double low = iLow(_Symbol, _Period, bar);
    double open = iOpen(_Symbol, _Period, bar);
    double close = iClose(_Symbol, _Period, bar);
    return (MathMin(open, close) - low) / _Point;
}
double GetClosePosition(int bar) {
    double high = iHigh(_Symbol, _Period, bar);
    double low = iLow(_Symbol, _Period, bar);
    double close = iClose(_Symbol, _Period, bar);
    return (close - low) / (high - low) * 100;
}
bool IsBullish(int bar) { return iClose(_Symbol, _Period, bar) > iOpen(_Symbol, _Period, bar); }
bool IsBearish(int bar) { return iClose(_Symbol, _Period, bar) < iOpen(_Symbol, _Period, bar); }
bool IsDoji(int bar) { return GetBodySize(bar) < 2; }

"""
    
    # En iyi 5 pattern için kod oluştur
    for i, pattern in enumerate(results[:5]):
        pattern_name = pattern['name'].replace(' ', '_')
        signal_type = "BUY" if "BUY" in pattern['name'] else "SELL"
        
        mql5_code += f"""
//+------------------------------------------------------------------+
//| {pattern['name']} - {pattern['success_rate']:.1f}% başarı
//| {pattern['description']}
//+------------------------------------------------------------------+
bool Check_{pattern_name}(int bar = 1)
{{
    // Başarı oranı: {pattern['success_rate']:.1f}%
    // Ortalama hareket: {pattern['avg_move']:.1f} pip
    // Test sayısı: {pattern['samples']}
    
"""
        
        # Pattern'e göre koşulları ekle
        if "Reversal_After_2Bearish" in pattern['name']:
            mql5_code += """    // 2 ardışık bearish + alt kapanış
    if(!IsBearish(bar) || !IsBearish(bar + 1)) return false;
    if(GetClosePosition(bar) >= 30) return false;
    if(GetBarSize(bar) < 8 || GetBarSize(bar) > 30) return false;
    return true;
"""
        elif "Reversal_After_2Bullish" in pattern['name']:
            mql5_code += """    // 2 ardışık bullish + üst kapanış
    if(!IsBullish(bar) || !IsBullish(bar + 1)) return false;
    if(GetClosePosition(bar) <= 70) return false;
    if(GetBarSize(bar) < 8 || GetBarSize(bar) > 30) return false;
    return true;
"""
        elif "Hammer" in pattern['name']:
            mql5_code += """    // Hammer pattern
    if(GetLowerShadow(bar) <= GetBodySize(bar) * 2) return false;
    if(GetLowerShadow(bar) < 5) return false;
    if(GetClosePosition(bar) <= 60) return false;
    if(GetBarSize(bar) < 8) return false;
    return true;
"""
        elif "ShootingStar" in pattern['name']:
            mql5_code += """    // Shooting Star pattern
    if(GetUpperShadow(bar) <= GetBodySize(bar) * 2) return false;
    if(GetUpperShadow(bar) < 5) return false;
    if(GetClosePosition(bar) >= 40) return false;
    if(GetBarSize(bar) < 8) return false;
    return true;
"""
        else:
            mql5_code += """    // Pattern koşulları buraya eklenecek
    return false;
"""
        
        mql5_code += "}\n"
    
    mql5_code += """
//+------------------------------------------------------------------+
//| Ana pattern kontrol fonksiyonu                                  |
//+------------------------------------------------------------------+
string CheckAllPatterns(int bar = 1)
{
"""
    
    for pattern in results[:5]:
        pattern_name = pattern['name'].replace(' ', '_')
        signal_type = "BUY" if "BUY" in pattern['name'] else "SELL"
        mql5_code += f"""    if(Check_{pattern_name}(bar)) return "{signal_type}";
"""
    
    mql5_code += """    return "NONE";
}
"""
    
    return mql5_code

def main():
    """Ana analiz"""
    df = load_and_analyze()
    results = find_best_combinations(df)
    
    print("\n🏆 EN İYİ PATTERN'LER:")
    print("=" * 50)
    
    for i, result in enumerate(results, 1):
        print(f"\n{i}. {result['name']}")
        print(f"   📊 Başarı: {result['success_rate']:.1f}%")
        print(f"   📈 Ortalama: {result['avg_move']:.1f} pip")
        print(f"   🔢 Test: {result['samples']} adet")
        print(f"   📝 {result['description']}")
    
    # MQL5 kodunu oluştur ve kaydet
    mql5_code = generate_mql5_templates(results)
    
    with open('XAUUSD_Patterns.mq5', 'w', encoding='utf-8') as f:
        f.write(mql5_code)
    
    print(f"\n💾 MQL5 kodları 'XAUUSD_Patterns.mq5' dosyasına kaydedildi")
    
    # Genel istatistikler
    print(f"\n📊 GENEL İSTATİSTİKLER:")
    print(f"   • Analiz edilen bar: {len(df):,}")
    print(f"   • Ortalama bar büyüklüğü: {df['BarSize'].mean():.1f} pip")
    print(f"   • En büyük pattern başarısı: {max(results, key=lambda x: x['success_rate'])['success_rate']:.1f}%")
    print(f"   • En çok test edilen pattern: {max(results, key=lambda x: x['samples'])['samples']:,} test")

if __name__ == "__main__":
    main()
