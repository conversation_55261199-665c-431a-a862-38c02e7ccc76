
//+------------------------------------------------------------------+
//| Pattern-Based Signal Functions                                   |
//+------------------------------------------------------------------+

// Bar metrikleri hesapla
double CalculateBarSize(int bar_index)
{
    return (iHigh(_Symbol, _Period, bar_index) - iLow(_Symbol, _Period, bar_index)) / _Point;
}

double CalculateBodySize(int bar_index)
{
    return MathAbs(iClose(_Symbol, _Period, bar_index) - iOpen(_Symbol, _Period, bar_index)) / _Point;
}

double CalculateUpperShadow(int bar_index)
{
    double high = iHigh(_Symbol, _Period, bar_index);
    double open = iOpen(_Symbol, _Period, bar_index);
    double close = iClose(_Symbol, _Period, bar_index);
    return (high - MathMax(open, close)) / _Point;
}

double CalculateLowerShadow(int bar_index)
{
    double low = iLow(_Symbol, _Period, bar_index);
    double open = iOpen(_Symbol, _Period, bar_index);
    double close = iClose(_Symbol, _Period, bar_index);
    return (MathMin(open, close) - low) / _Point;
}

int GetConsecutiveBars(int bar_index)
{
    double current_close = iClose(_Symbol, _Period, bar_index);
    double current_open = iOpen(_Symbol, _Period, bar_index);
    bool current_bullish = current_close > current_open;
    
    int count = 1;
    for(int i = bar_index + 1; i < bar_index + 10; i++)
    {
        double close = iClose(_Symbol, _Period, i);
        double open = iOpen(_Symbol, _Period, i);
        bool bullish = close > open;
        
        if(bullish == current_bullish)
            count++;
        else
            break;
    }
    return count;
}

