//+------------------------------------------------------------------+
//|                                                   ProfessionalTradePanel.mq5 |
//|                        Horizon Systems                          |
//|                        https://www.mql5.com                     |
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "CDialog Tabanlı Profesyonel Sürüklenebilir Panel"

#include <Controls\Dialog.mqh>
#include <Controls\Label.mqh>
#include <Controls\Button.mqh>
#include <Trade\Trade.mqh>

// Panel boyutları
#define PANEL_WIDTH     380
#define PANEL_HEIGHT    320
#define BUTTON_WIDTH    110
#define BUTTON_HEIGHT   25
#define MARGIN          10

// TradePanel sınıfı
class TradePanel : public CDialog
{
private:
    CLabel m_title, m_dailyPL, m_positionCount, m_totalVolume, m_totalProfit, m_profitLossCount;
    CLabel m_positionList[6];
    CButton m_btnCloseProfitable, m_btnCloseLosing, m_btnCloseAll;
    datetime m_lastUpdate;

public:
    TradePanel();
    ~TradePanel();
    virtual bool Create(const long chart, const string name, const int subwin, const int x1, const int y1);
    virtual void OnTick();
    virtual bool OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam);
    void UpdateData();
    void CreateElements();
    double CalculateDailyPL();
    void CloseProfitablePositions();
    void CloseLosingPositions();
    void CloseAllPositions();
};

TradePanel::TradePanel() { m_lastUpdate = 0; }
TradePanel::~TradePanel() {}

bool TradePanel::Create(const long chart, const string name, const int subwin, const int x1, const int y1)
{
    if (!CDialog::Create(chart, name, subwin, x1, y1, x1 + PANEL_WIDTH, y1 + PANEL_HEIGHT))
        return false;

    // Stil ayarları bu sınıf için geçerli değil, yorumlandı
    // this->Background(ColorToARGB(25,25,25));
    // this->BorderType(BORDER_FLAT);
    // this->Color(ColorToARGB(100,100,100));
    // this->SetInteger(OBJPROP_ZORDER, 1000);

    this->Caption("📊 İşlem Yönetim Paneli");

    CreateElements();
    UpdateData();

    return true;
}

void TradePanel::CreateElements()
{
    int y_pos = 30;

    m_dailyPL.Create(m_chart_id, "DailyPL", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 20);
    m_dailyPL.Text("💰 Günlük P&L: $0.00");
    m_dailyPL.Color(clrYellow);
    m_dailyPL.FontSize(10);
    Add(m_dailyPL);
    y_pos += 25;

    m_positionCount.Create(m_chart_id, "PositionCount", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 18);
    m_positionCount.Text("📈 Açık Pozisyon: 0");
    m_positionCount.Color(clrLightBlue);
    m_positionCount.FontSize(9);
    Add(m_positionCount);
    y_pos += 22;

    m_totalVolume.Create(m_chart_id, "TotalVolume", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 18);
    m_totalVolume.Text("📊 Toplam Hacim: 0.00");
    m_totalVolume.Color(clrLightGray);
    m_totalVolume.FontSize(9);
    Add(m_totalVolume);
    y_pos += 22;

    m_totalProfit.Create(m_chart_id, "TotalProfit", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 18);
    m_totalProfit.Text("💵 Toplam P&L: $0.00");
    m_totalProfit.Color(clrWhite);
    m_totalProfit.FontSize(10);
    Add(m_totalProfit);
    y_pos += 22;

    m_profitLossCount.Create(m_chart_id, "ProfitLossCount", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 18);
    m_profitLossCount.Text("📊 Karlı: 0 | Zararlı: 0");
    m_profitLossCount.Color(clrWhite);
    m_profitLossCount.FontSize(9);
    Add(m_profitLossCount);
    y_pos += 30;

    int btn_y = y_pos;

    m_btnCloseProfitable.Create(m_chart_id, "BtnProfit", m_subwin, MARGIN, btn_y, MARGIN + BUTTON_WIDTH, btn_y + BUTTON_HEIGHT);
    m_btnCloseProfitable.Text("💚 Karlı Kapat");
    m_btnCloseProfitable.Color(clrWhite);
    m_btnCloseProfitable.ColorBackground(clrGreen);
    m_btnCloseProfitable.FontSize(9);
    Add(m_btnCloseProfitable);

    m_btnCloseLosing.Create(m_chart_id, "BtnLoss", m_subwin, MARGIN + BUTTON_WIDTH + 10, btn_y, MARGIN + 2 * BUTTON_WIDTH + 10, btn_y + BUTTON_HEIGHT);
    m_btnCloseLosing.Text("❤️ Zarar Kapat");
    m_btnCloseLosing.Color(clrWhite);
    m_btnCloseLosing.ColorBackground(clrRed);
    m_btnCloseLosing.FontSize(9);
    Add(m_btnCloseLosing);

    btn_y += BUTTON_HEIGHT + 10;

    m_btnCloseAll.Create(m_chart_id, "BtnAll", m_subwin, MARGIN, btn_y, MARGIN + 2 * BUTTON_WIDTH + 10, btn_y + BUTTON_HEIGHT);
    m_btnCloseAll.Text("🚫 Hepsini Kapat");
    m_btnCloseAll.Color(clrWhite);
    m_btnCloseAll.ColorBackground(clrOlive);
    m_btnCloseAll.FontSize(9);
    Add(m_btnCloseAll);

    y_pos = btn_y + BUTTON_HEIGHT + 20;

    for (int i = 0; i < 6; i++)
    {
        m_positionList[i].Create(m_chart_id, "Pos" + IntegerToString(i), m_subwin,
                                 MARGIN, y_pos + i * 18, PANEL_WIDTH - MARGIN, y_pos + (i + 1) * 18);
        m_positionList[i].Text("");
        m_positionList[i].Color(clrLightGray);
        m_positionList[i].FontSize(8);
        Add(m_positionList[i]);
    }
}

void TradePanel::UpdateData()
{
    int positionCount = 0;
    double totalVolume = 0;
    double totalProfit = 0;
    int profitableCount = 0, losingCount = 0;

    for (int i = 0; i < 6; i++)
        m_positionList[i].Text("");

    int displayCount = 0;
    for (int i = 0; i < PositionsTotal() && displayCount < 6; i++)
    {
        string symbol = PositionGetSymbol(i);
        if (symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);

        positionCount++;
        totalVolume += volume;
        totalProfit += profit;

        if (profit > 0) profitableCount++;
        else if (profit < 0) losingCount++;

        string typeStr = (type == POSITION_TYPE_BUY) ? "BUY" : "SELL";
        string profitStr = (profit >= 0) ? "+" + DoubleToString(profit, 2) : DoubleToString(profit, 2);
        color profitColor = (profit >= 0) ? clrLimeGreen : clrTomato;

        string posText = StringFormat("%s %s %.2f | $%s", typeStr, symbol, volume, profitStr);

        m_positionList[displayCount].Text(posText);
        m_positionList[displayCount].Color(profitColor);
        displayCount++;
    }

    if (PositionsTotal() > 6)
    {
        string moreText = "... ve " + IntegerToString(PositionsTotal() - 6) + " pozisyon daha";
        if (displayCount < 6)
        {
            m_positionList[displayCount].Text(moreText);
            m_positionList[displayCount].Color(clrGray);
        }
    }

    double dailyPL = CalculateDailyPL();

    m_dailyPL.Text("💰 Günlük P&L: $" + DoubleToString(dailyPL, 2));
    m_dailyPL.Color((dailyPL >= 0) ? clrLimeGreen : clrTomato);
    m_positionCount.Text("📈 Açık Pozisyon: " + IntegerToString(positionCount));
    m_totalVolume.Text("📊 Toplam Hacim: " + DoubleToString(totalVolume, 2));
    m_totalProfit.Text("💵 Toplam P&L: $" + DoubleToString(totalProfit, 2));
    m_totalProfit.Color((totalProfit >= 0) ? clrLimeGreen : clrTomato);
    m_profitLossCount.Text("📊 Karlı: " + IntegerToString(profitableCount) + " | Zararlı: " + IntegerToString(losingCount));
}

double TradePanel::CalculateDailyPL()
{
    double dailyPL = 0;
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    HistorySelect(todayStart, TimeCurrent());

    for (int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if (ticket > 0)
        {
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
            if (dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);
                dailyPL += (profit + commission + swap);
            }
        }
    }

    for (int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if (symbol != "")
            dailyPL += PositionGetDouble(POSITION_PROFIT);
    }

    return dailyPL;
}

void TradePanel::OnTick()
{
    if (TimeCurrent() - m_lastUpdate >= 2)
    {
        UpdateData();
        m_lastUpdate = TimeCurrent();
    }
}

bool TradePanel::OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if (id == CHARTEVENT_CLICK)
    {
        string clickedObjectName = sparam;
        if (clickedObjectName == m_btnCloseProfitable.Name()) { CloseProfitablePositions(); return true; }
        else if (clickedObjectName == m_btnCloseLosing.Name()) { CloseLosingPositions(); return true; }
        else if (clickedObjectName == m_btnCloseAll.Name()) { CloseAllPositions(); return true; }
    }
    return CDialog::OnEvent(id, lparam, dparam, sparam);
}

void TradePanel::CloseProfitablePositions()
{
    CTrade trade;
    int closedCount = 0;
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if (symbol == "") continue;
        double profit = PositionGetDouble(POSITION_PROFIT);
        if (profit > 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if (trade.PositionClose(ticket))
                closedCount++;
        }
    }
    UpdateData();
}

void TradePanel::CloseLosingPositions()
{
    CTrade trade;
    int closedCount = 0;
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if (symbol == "") continue;
        double profit = PositionGetDouble(POSITION_PROFIT);
        if (profit < 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if (trade.PositionClose(ticket))
                closedCount++;
        }
    }
    UpdateData();
}

void TradePanel::CloseAllPositions()
{
    CTrade trade;
    int closedCount = 0;
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if (symbol == "") continue;
        ulong ticket = PositionGetInteger(POSITION_TICKET);
        trade.PositionClose(ticket);
    }
    UpdateData();
}

// Global değişken
TradePanel* g_tradePanel = NULL;

int OnInit()
{
    g_tradePanel = new TradePanel();
    if (!g_tradePanel.Create(0, "TradePanelDialog", 0, 50, 50))
    {
        Print("❌ Panel oluşturulamadı.");
        delete g_tradePanel;
        g_tradePanel = NULL;
        return INIT_FAILED;
    }
    Print("✅ Panel başlatıldı.");
    return INIT_SUCCEEDED;
}

void OnDeinit(const int reason)
{
    if (g_tradePanel != NULL)
    {
        g_tradePanel.Destroy(reason);
        delete g_tradePanel;
        g_tradePanel = NULL;
    }
}

void OnTick()
{
    if (g_tradePanel != NULL)
        g_tradePanel.OnTick();
}

void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if (g_tradePanel != NULL)
        g_tradePanel.OnEvent(id, lparam, dparam, sparam);
}
